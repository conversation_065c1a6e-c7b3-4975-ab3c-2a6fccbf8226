services:
  app:
    image: real-time-ca-service
    ports:
      - "8181:8181"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    volumes:
      - ./service.toml:/app/service.toml
      - ./logs:/app/logs # Map log directory to host
    network_mode: "host"
    # Command-line arguments for the service (override config file settings)
    # Available flags:
    #   --only-api: Only start API server without background workers
    #   --enable-server: Enable HTTP server
    #   --log-level=LEVEL: Set log level (debug, info, warn, error, fatal, panic)
    # Examples:
    #   command: ["/app/server", "--only-api"]
    #   command: ["/app/server", "--enable-server", "--log-level=debug"]
    #   command: ["/app/server", "--only-api", "--enable-server", "--log-level=info"]