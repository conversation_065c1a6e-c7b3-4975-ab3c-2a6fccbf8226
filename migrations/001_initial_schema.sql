create table if not exists public.twitter_users
(
    id                bigserial
        primary key,
    created_at        timestamp with time zone default CURRENT_TIMESTAMP not null,
    updated_at        timestamp with time zone default CURRENT_TIMESTAMP not null,
    user_id           varchar(255),
    screen_name       varchar(100)                                       not null,
    name              varchar(255)                                       not null,
    followers_count   integer,
    is_verified       boolean                  default false,
    profile_image_url text,
    fetched_at        timestamp with time zone default CURRENT_TIMESTAMP
);

alter table public.twitter_users
    owner to postgres;

create unique index if not exists idx_twitter_users_screen_name
    on public.twitter_users (screen_name);

create unique index if not exists idx_twitter_users_user_id
    on public.twitter_users (user_id);

create table if not exists public.tweets
(
    id                      bigserial
        primary key,
    created_at              timestamp with time zone default CURRENT_TIMESTAMP not null,
    updated_at              timestamp with time zone default CURRENT_TIMESTAMP not null,
    tweet_id                varchar(255),
    user_id_fk              varchar(255)                                       not null
        constraint fk_twitter_users_tweets
            references public.twitter_users (user_id),
    text_content            text                                               not null,
    full_tweet_json         jsonb,
    published_at            timestamp with time zone                           not null,
    views_count             bigint,
    contains_target_keyword boolean                  default false,
    ingested_at             timestamp with time zone default CURRENT_TIMESTAMP
);

alter table public.tweets
    owner to postgres;

create index if not exists idx_tweets_contains_target_keyword
    on public.tweets (contains_target_keyword);

create index if not exists idx_tweets_published_at
    on public.tweets (published_at);

create index if not exists idx_tweets_full_tweet_json
    on public.tweets using gin (full_tweet_json);

create index if not exists idx_tweets_user_id_fk
    on public.tweets (user_id_fk);

create unique index if not exists idx_tweets_tweet_id
    on public.tweets (tweet_id);

create unique index if not exists idx_tweet_published
    on public.tweets (tweet_id, published_at);

create table if not exists public.extracted_contract_addresses
(
    id                  bigserial
        primary key,
    created_at          timestamp with time zone default CURRENT_TIMESTAMP not null,
    updated_at          timestamp with time zone default CURRENT_TIMESTAMP not null,
    ca_address          varchar(255)                                       not null,
    detected_chain_type varchar(50),
    extracted_at        timestamp with time zone default CURRENT_TIMESTAMP
);

alter table public.extracted_contract_addresses
    owner to postgres;

create unique index if not exists idx_extracted_contract_addresses_ca_address
    on public.extracted_contract_addresses (ca_address);

create table if not exists public.tweet_contract_addresses
(
    extracted_contract_address_id bigint not null
        constraint fk_tweet_contract_addresses_extracted_contract_address
            references public.extracted_contract_addresses,
    tweet_id                      bigint not null
        constraint fk_tweet_contract_addresses_tweet
            references public.tweets,
    primary key (extracted_contract_address_id, tweet_id)
);

alter table public.tweet_contract_addresses
    owner to postgres;

create table if not exists public.recognized_cas
(
    id                       bigserial
        primary key,
    created_at               timestamp with time zone default CURRENT_TIMESTAMP not null,
    updated_at               timestamp with time zone default CURRENT_TIMESTAMP not null,
    ca_address               varchar(255)
        constraint fk_extracted_contract_addresses_recognized_ca
            references public.extracted_contract_addresses (ca_address),
    chain_id                 varchar(100)                                       not null,
    token_name_hint          varchar(255),
    added_at                 timestamp with time zone default CURRENT_TIMESTAMP,
    last_checked_for_data_at timestamp with time zone,
    reference_count          bigint                   default 0
);

alter table public.recognized_cas
    owner to postgres;


create index if not exists idx_recognized_cas_last_checked_for_data_at
    on public.recognized_cas (last_checked_for_data_at);

create index if not exists idx_recognized_cas_chain_id
    on public.recognized_cas (chain_id);

create unique index if not exists idx_recognized_cas_ca_address
    on public.recognized_cas (ca_address);

create table if not exists public.token_details
(
    id                    bigserial
        primary key,
    created_at            timestamp with time zone default CURRENT_TIMESTAMP                not null,
    updated_at            timestamp with time zone default CURRENT_TIMESTAMP                not null,
    ca_address_fk         varchar(255)
        constraint fk_recognized_cas_token_details
            references public.recognized_cas (ca_address)
        constraint fk_extracted_contract_addresses_token_details
            references public.extracted_contract_addresses (ca_address),
    chain_id              varchar(100)                                                      not null,
    token_name            varchar(255),
    symbol                varchar(50),
    token_logo_url        text,
    token_twitter_url     text,
    pair_created_at       timestamp with time zone,
    holder_count          bigint,
    market_cap_usd        numeric(30, 8),
    price_usd             numeric(30, 18),
    full_dexscreener_json jsonb,
    last_updated_at       timestamp with time zone default CURRENT_TIMESTAMP,
    source                varchar(50)              default 'dexscreener'::character varying not null
);

alter table public.token_details
    owner to postgres;

create index if not exists idx_token_details_full_dexscreener_json
    on public.token_details using gin (full_dexscreener_json);

create index if not exists idx_token_details_chain_id
    on public.token_details (chain_id);

create unique index if not exists idx_token_details_ca_address_source
    on public.token_details (ca_address_fk, source);

create unique index if not exists idx_token_details_ca_chain_source
    on public.token_details (ca_address_fk, chain_id);

create index if not exists idx_token_details_ca_address_fk
    on public.token_details (ca_address_fk);

alter table public.recognized_cas
    drop constraint fk_extracted_contract_addresses_recognized_ca;

alter table public.recognized_cas
    add constraint fk_extracted_contract_addresses_recognized_ca
        foreign key (ca_address) references public.extracted_contract_addresses (ca_address);

alter table public.token_details
    drop constraint fk_recognized_cas_token_details;

alter table public.token_details
    add constraint fk_recognized_cas_token_details
        foreign key (ca_address_fk) references public.recognized_cas (ca_address);

alter table public.token_details
    drop constraint fk_extracted_contract_addresses_token_details;

alter table public.token_details
    add constraint fk_extracted_contract_addresses_token_details
        foreign key (ca_address_fk) references public.extracted_contract_addresses (ca_address);

alter table public.tweets
    drop constraint fk_twitter_users_tweets;

alter table public.tweets
    add constraint fk_twitter_users_tweets
        foreign key (user_id_fk) references public.twitter_users (user_id);
