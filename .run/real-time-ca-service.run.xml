<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="real-time-ca-service" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="real-time-ca-service" />
    <working_directory value="$PROJECT_DIR$" />
    <parameters value="-enable-http -only-api -log-level=debug" />
    <envs>
      <env name="http_proxy" value="http://127.0.0.1:7897" />
      <env name="https_proxy" value="http://127.0.0.1:7897" />
      <env name="all_proxy" value="socks5://127.0.0.1:7897" />
    </envs>
    <EXTENSION ID="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
      </ENTRIES>
    </EXTENSION>
    <kind value="PACKAGE" />
    <package value="real-time-ca-service/cmd/server" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/server/main.go" />
    <method v="2" />
  </configuration>
</component>