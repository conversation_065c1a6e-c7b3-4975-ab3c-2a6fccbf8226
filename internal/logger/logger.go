package logger

import (
	"io"
	"os"
	"path/filepath" // New import
	"time"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs" // New import
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

const (
	defaultLogDir     = "logs"
	logFilePatternTpl = "service-%Y-%m-%d-%H.log" // Template for log file names
	currentLogSymlink = "current.log"
)

// Init initializes the global logger
func Init(level string, pretty bool) {
	// Set the global logger
	zerolog.TimeFieldFormat = time.RFC3339
	zerolog.SetGlobalLevel(parseLevel(level))

	// --- File Rotation Setup ---
	logDir := defaultLogDir

	// Fallback logger setup function
	setupFallbackLogger := func(errMsg string, errArgs ...interface{}) {
		// Log the error that caused the fallback using a basic stderr logger
		// to avoid issues with the global logger not being fully set up.
		tempLogger := zerolog.New(os.Stderr).With().Timestamp().Logger()
		tempLogger.Error().Msgf(errMsg, errArgs...)

		var fallbackOutput io.Writer = os.Stdout
		if pretty {
			fallbackOutput = zerolog.ConsoleWriter{
				Out:        os.Stdout,
				TimeFormat: time.RFC3339,
			}
		}
		log.Logger = zerolog.New(fallbackOutput).With().Timestamp().Caller().Logger()
		log.Warn().Msg("Logger initialized to write to STDOUT only due to issues with file logging setup.")
	}

	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		if mkDirErr := os.MkdirAll(logDir, 0755); mkDirErr != nil {
			setupFallbackLogger("Failed to create log directory '%s': %v.", logDir, mkDirErr)
			return
		}
	}

	filePathPattern := filepath.Join(logDir, logFilePatternTpl)
	rl, err := rotatelogs.New(
		filePathPattern,
		rotatelogs.WithLinkName(filepath.Join(logDir, currentLogSymlink)), // Optional: symlink to current log file
		rotatelogs.WithMaxAge(7*24*time.Hour),                             // Keep logs for 7 days
		rotatelogs.WithRotationTime(time.Hour),                            // Rotate every hour
	)

	if err != nil {
		setupFallbackLogger("Failed to initialize file rotation: %v.", err)
		return
	}
	// --- End File Rotation Setup ---

	var writers []io.Writer
	writers = append(writers, rl) // Log to file

	if pretty { // If pretty is true, also log to console in pretty format
		consoleWriter := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: time.RFC3339,
		}
		writers = append(writers, consoleWriter)
	}

	multiWriter := io.MultiWriter(writers...)
	log.Logger = zerolog.New(multiWriter).With().Timestamp().Caller().Logger()

	log.Info().Msgf("Logger initialized. Log level: %s. Writing logs to directory: %s. Pretty console output: %t", level, logDir, pretty)
}

// parseLevel parses the log level string
func parseLevel(level string) zerolog.Level {
	switch level {
	case "debug":
		return zerolog.DebugLevel
	case "info":
		return zerolog.InfoLevel
	case "warn":
		return zerolog.WarnLevel
	case "error":
		return zerolog.ErrorLevel
	case "fatal":
		return zerolog.FatalLevel
	case "panic":
		return zerolog.PanicLevel
	default:
		return zerolog.InfoLevel
	}
}

// Debug logs a debug message
func Debug() *zerolog.Event {
	return log.Debug()
}

// Info logs an info message
func Info() *zerolog.Event {
	return log.Info()
}

// Warn logs a warning message
func Warn() *zerolog.Event {
	return log.Warn()
}

// Error logs an error message
func Error() *zerolog.Event {
	return log.Error()
}

// Fatal logs a fatal message and exits
func Fatal() *zerolog.Event {
	return log.Fatal()
}

// Panic logs a panic message and panics
func Panic() *zerolog.Event {
	return log.Panic()
}
