package services

import (
	"context"
	"fmt"
	"strconv"
	"testing"
	"time"

	"real-time-ca-service/internal/config"

	"github.com/alicebob/miniredis/v2"
	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestRedisForCleanup(t *testing.T) (*RedisService, func()) {
	// Start a mini Redis server for testing
	mr, err := miniredis.Run()
	require.NoError(t, err)

	port, err := strconv.Atoi(mr.Port())
	require.NoError(t, err)

	cfg := config.RedisConfig{
		Host:                mr.Host(),
		Port:                port,
		LockTimeout:         30 * time.Second,
		LockRetryDelay:      100 * time.Millisecond,
		LockMaxRetries:      10,
		LockRefreshInterval: 10 * time.Second,
		LockCleanupTimeout:  30 * time.Second,
	}

	// Create Redis client with miniredis address
	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
		DB:   0,
	})

	// Create Redis service manually for testing
	redisService := &RedisService{
		client: client,
		config: cfg,
	}

	// Initialize the locker
	redisService.locker = redislock.New(client)

	cleanup := func() {
		client.Close()
		mr.Close()
	}

	return redisService, cleanup
}

func TestDistributedLockManager_LockTracking(t *testing.T) {
	redisService, cleanup := setupTestRedisForCleanup(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	ctx := context.Background()

	t.Run("track active locks", func(t *testing.T) {
		// Initially no locks
		assert.Equal(t, 0, lockManager.GetActiveLockCount())
		assert.Empty(t, lockManager.GetActiveLockKeys())

		// Acquire first lock
		lock1, err := lockManager.AcquireLock(ctx, "test_lock_1", nil)
		require.NoError(t, err)
		require.NotNil(t, lock1)

		assert.Equal(t, 1, lockManager.GetActiveLockCount())
		assert.Contains(t, lockManager.GetActiveLockKeys(), "test_lock_1")

		// Acquire second lock
		lock2, err := lockManager.AcquireLock(ctx, "test_lock_2", nil)
		require.NoError(t, err)
		require.NotNil(t, lock2)

		assert.Equal(t, 2, lockManager.GetActiveLockCount())
		keys := lockManager.GetActiveLockKeys()
		assert.Contains(t, keys, "test_lock_1")
		assert.Contains(t, keys, "test_lock_2")

		// Release first lock
		err = lock1.ReleaseLock()
		assert.NoError(t, err)

		assert.Equal(t, 1, lockManager.GetActiveLockCount())
		assert.Contains(t, lockManager.GetActiveLockKeys(), "test_lock_2")
		assert.NotContains(t, lockManager.GetActiveLockKeys(), "test_lock_1")

		// Release second lock
		err = lock2.ReleaseLock()
		assert.NoError(t, err)

		assert.Equal(t, 0, lockManager.GetActiveLockCount())
		assert.Empty(t, lockManager.GetActiveLockKeys())
	})
}

func TestDistributedLockManager_CleanupAllLocks(t *testing.T) {
	redisService, cleanup := setupTestRedisForCleanup(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	ctx := context.Background()

	t.Run("cleanup with no active locks", func(t *testing.T) {
		err := lockManager.CleanupAllLocks(ctx)
		assert.NoError(t, err)
	})

	t.Run("cleanup with multiple active locks", func(t *testing.T) {
		// Acquire multiple locks
		locks := make([]*DistributedLock, 5)
		for i := 0; i < 5; i++ {
			lockKey := fmt.Sprintf("test_cleanup_lock_%d", i)
			lock, err := lockManager.AcquireLock(ctx, lockKey, nil)
			require.NoError(t, err)
			require.NotNil(t, lock)
			locks[i] = lock
		}

		// Verify locks are tracked
		assert.Equal(t, 5, lockManager.GetActiveLockCount())

		// Cleanup all locks
		err := lockManager.CleanupAllLocks(ctx)
		assert.NoError(t, err)

		// Verify all locks are cleaned up
		assert.Equal(t, 0, lockManager.GetActiveLockCount())
		assert.Empty(t, lockManager.GetActiveLockKeys())
	})

	t.Run("cleanup with successful completion", func(t *testing.T) {
		// Acquire a lock
		lock, err := lockManager.AcquireLock(ctx, "success_test_lock", nil)
		require.NoError(t, err)
		require.NotNil(t, lock)

		// Cleanup should succeed
		err = lockManager.CleanupAllLocks(ctx)
		assert.NoError(t, err)

		// Verify lock was cleaned up
		assert.Equal(t, 0, lockManager.GetActiveLockCount())
	})
}

func TestDistributedLockManager_CleanupNilManager(t *testing.T) {
	var lockManager *DistributedLockManager
	ctx := context.Background()

	err := lockManager.CleanupAllLocks(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "lock manager is nil")
}

func TestDistributedLockManager_GetActiveLockMethods(t *testing.T) {
	t.Run("nil manager", func(t *testing.T) {
		var lockManager *DistributedLockManager

		assert.Equal(t, 0, lockManager.GetActiveLockCount())
		assert.Nil(t, lockManager.GetActiveLockKeys())
	})

	redisService, cleanup := setupTestRedisForCleanup(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	ctx := context.Background()

	t.Run("empty manager", func(t *testing.T) {
		assert.Equal(t, 0, lockManager.GetActiveLockCount())
		assert.Empty(t, lockManager.GetActiveLockKeys())
	})

	t.Run("with locks", func(t *testing.T) {
		// Acquire some locks
		lock1, err := lockManager.AcquireLock(ctx, "key1", nil)
		require.NoError(t, err)
		lock2, err := lockManager.AcquireLock(ctx, "key2", nil)
		require.NoError(t, err)

		assert.Equal(t, 2, lockManager.GetActiveLockCount())
		keys := lockManager.GetActiveLockKeys()
		assert.Len(t, keys, 2)
		assert.Contains(t, keys, "key1")
		assert.Contains(t, keys, "key2")

		// Clean up
		lock1.ReleaseLock()
		lock2.ReleaseLock()
	})
}

func TestDistributedLockManager_CleanupTimeout(t *testing.T) {
	redisService, cleanup := setupTestRedisForCleanup(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)

	t.Run("cleanup respects context timeout", func(t *testing.T) {
		// Acquire a lock
		lock, err := lockManager.AcquireLock(context.Background(), "context_timeout_test", nil)
		require.NoError(t, err)
		require.NotNil(t, lock)

		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		// Wait for context to timeout
		time.Sleep(2 * time.Millisecond)

		// Cleanup should respect the context timeout
		err = lockManager.CleanupAllLocks(ctx)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cleanup completed with")

		// Clean up manually
		lock.ReleaseLock()
	})
}
