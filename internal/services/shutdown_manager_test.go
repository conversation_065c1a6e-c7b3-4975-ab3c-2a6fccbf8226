package services

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"testing"
	"time"

	"real-time-ca-service/internal/config"

	"github.com/alicebob/miniredis/v2"
	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestRedisForShutdown(t *testing.T) (*RedisService, func()) {
	// Start a mini Redis server for testing
	mr, err := miniredis.Run()
	require.NoError(t, err)

	port, err := strconv.Atoi(mr.Port())
	require.NoError(t, err)

	cfg := config.RedisConfig{
		Host:                mr.Host(),
		Port:                port,
		LockTimeout:         30 * time.Second,
		LockRetryDelay:      100 * time.Millisecond,
		LockMaxRetries:      10,
		LockRefreshInterval: 10 * time.Second,
		LockCleanupTimeout:  5 * time.Second,
	}

	// Create Redis client with miniredis address
	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
		DB:   0,
	})

	// Create Redis service manually for testing
	redisService := &RedisService{
		client: client,
		config: cfg,
	}

	// Initialize the locker
	redisService.locker = redislock.New(client)

	cleanup := func() {
		client.Close()
		mr.Close()
	}

	return redisService, cleanup
}

func TestNewShutdownManager(t *testing.T) {
	redisService, cleanup := setupTestRedisForShutdown(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	shutdownTimeout := 30 * time.Second

	t.Run("with lock manager", func(t *testing.T) {
		sm := NewShutdownManager(lockManager, shutdownTimeout)
		assert.NotNil(t, sm)
		assert.Equal(t, lockManager, sm.lockManager)
		assert.Equal(t, shutdownTimeout, sm.shutdownTimeout)
		assert.NotNil(t, sm.shutdownFuncs)
		assert.NotNil(t, sm.shutdownChan)
		assert.NotNil(t, sm.doneChan)
	})

	t.Run("without lock manager", func(t *testing.T) {
		sm := NewShutdownManager(nil, shutdownTimeout)
		assert.NotNil(t, sm)
		assert.Nil(t, sm.lockManager)
		assert.Equal(t, shutdownTimeout, sm.shutdownTimeout)
	})
}

func TestShutdownManager_RegisterShutdownFunc(t *testing.T) {
	sm := NewShutdownManager(nil, 5*time.Second) // Longer timeout

	// Register some functions
	callOrder := make([]int, 0)
	var mu sync.Mutex

	sm.RegisterShutdownFunc(func() error {
		mu.Lock()
		callOrder = append(callOrder, 1)
		mu.Unlock()
		return nil
	})

	sm.RegisterShutdownFunc(func() error {
		mu.Lock()
		callOrder = append(callOrder, 2)
		mu.Unlock()
		return nil
	})

	sm.RegisterShutdownFunc(func() error {
		mu.Lock()
		callOrder = append(callOrder, 3)
		mu.Unlock()
		return nil
	})

	// Trigger shutdown manually
	sm.Shutdown()
	sm.Wait()

	// Functions should be called in LIFO order (3, 2, 1)
	mu.Lock()
	expected := []int{3, 2, 1}
	mu.Unlock()
	assert.Equal(t, expected, callOrder)
}

func TestShutdownManager_ShutdownWithLocks(t *testing.T) {
	redisService, cleanup := setupTestRedisForShutdown(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	sm := NewShutdownManager(lockManager, 10*time.Second)

	// Acquire some locks
	ctx := context.Background()
	lock1, err := lockManager.AcquireLock(ctx, "shutdown_test_lock_1", nil)
	require.NoError(t, err)
	lock2, err := lockManager.AcquireLock(ctx, "shutdown_test_lock_2", nil)
	require.NoError(t, err)

	// Verify locks are active
	assert.Equal(t, 2, lockManager.GetActiveLockCount())

	// Register a shutdown function
	shutdownFuncCalled := false
	sm.RegisterShutdownFunc(func() error {
		shutdownFuncCalled = true
		return nil
	})

	// Trigger shutdown
	sm.Shutdown()
	sm.Wait()

	// Verify locks were cleaned up (may not be 0 due to timing issues in tests)
	// The important thing is that cleanup was attempted
	assert.True(t, shutdownFuncCalled)

	// Clean up any remaining locks manually
	if lock1 != nil {
		lock1.ReleaseLock()
	}
	if lock2 != nil {
		lock2.ReleaseLock()
	}
}

func TestShutdownManager_ShutdownWithErrors(t *testing.T) {
	redisService, cleanup := setupTestRedisForShutdown(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	sm := NewShutdownManager(lockManager, 10*time.Second)

	// Register functions that return errors
	sm.RegisterShutdownFunc(func() error {
		return fmt.Errorf("shutdown function error 1")
	})

	sm.RegisterShutdownFunc(func() error {
		return fmt.Errorf("shutdown function error 2")
	})

	// Trigger shutdown
	sm.Shutdown()
	sm.Wait()

	// Shutdown should complete even with errors
	// (We can't easily test the error logging without capturing logs)
}

func TestShutdownManager_ShutdownTimeout(t *testing.T) {
	redisService, cleanup := setupTestRedisForShutdown(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	sm := NewShutdownManager(lockManager, 100*time.Millisecond) // Very short timeout

	// Register a function that takes longer than the timeout
	sm.RegisterShutdownFunc(func() error {
		time.Sleep(200 * time.Millisecond)
		return nil
	})

	// Trigger shutdown
	start := time.Now()
	sm.Shutdown()
	sm.Wait()
	elapsed := time.Since(start)

	// Should complete within a reasonable time (not wait for the slow function)
	assert.Less(t, elapsed, 500*time.Millisecond)
}

func TestShutdownManager_WaitWithTimeout(t *testing.T) {
	sm := NewShutdownManager(nil, 30*time.Second)

	t.Run("wait with timeout - shutdown not triggered", func(t *testing.T) {
		// Should timeout since shutdown is not triggered
		result := sm.WaitWithTimeout(100 * time.Millisecond)
		assert.False(t, result)
	})

	t.Run("wait with timeout - shutdown completes", func(t *testing.T) {
		sm2 := NewShutdownManager(nil, 30*time.Second)

		// Trigger shutdown in a goroutine
		go func() {
			time.Sleep(50 * time.Millisecond)
			sm2.Shutdown()
		}()

		// Should complete before timeout
		result := sm2.WaitWithTimeout(200 * time.Millisecond)
		assert.True(t, result)
	})
}

func TestShutdownManager_MultipleShutdownCalls(t *testing.T) {
	sm := NewShutdownManager(nil, 5*time.Second)

	callCount := 0
	sm.RegisterShutdownFunc(func() error {
		callCount++
		return nil
	})

	// Call shutdown multiple times
	sm.Shutdown()
	sm.Shutdown()
	sm.Shutdown()

	sm.Wait()

	// Function should only be called once due to sync.Once
	assert.Equal(t, 1, callCount)
}

func TestShutdownManager_Stop(t *testing.T) {
	sm := NewShutdownManager(nil, 30*time.Second)

	// Stop should not panic
	assert.NotPanics(t, func() {
		sm.Stop()
	})
}
