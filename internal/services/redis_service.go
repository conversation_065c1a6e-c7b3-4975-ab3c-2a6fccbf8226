package services

import (
	"context"
	"fmt"
	"time"

	"real-time-ca-service/internal/config"

	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
)

// RedisService handles Redis connections and distributed locking
type RedisService struct {
	client *redis.Client
	locker *redislock.Client
	config config.RedisConfig
}

// NewRedisService creates a new Redis service with the given configuration
func NewRedisService(cfg config.RedisConfig) (*RedisService, error) {
	// Set default values if not configured
	if cfg.Host == "" {
		cfg.Host = "localhost"
	}
	if cfg.Port == 0 {
		cfg.Port = 6379
	}
	if cfg.PoolSize == 0 {
		cfg.PoolSize = 10
	}
	if cfg.MinIdleConns == 0 {
		cfg.MinIdleConns = 2
	}
	if cfg.MaxRetries == 0 {
		cfg.MaxRetries = 3
	}
	if cfg.DialTimeout == 0 {
		cfg.DialTimeout = 5 * time.Second
	}
	if cfg.ReadTimeout == 0 {
		cfg.ReadTimeout = 3 * time.Second
	}
	if cfg.WriteTimeout == 0 {
		cfg.WriteTimeout = 3 * time.Second
	}
	if cfg.PoolTimeout == 0 {
		cfg.PoolTimeout = 4 * time.Second
	}
	if cfg.IdleTimeout == 0 {
		cfg.IdleTimeout = 5 * time.Minute
	}
	if cfg.IdleCheckFrequency == 0 {
		cfg.IdleCheckFrequency = 1 * time.Minute
	}

	// Set default distributed locking values
	if cfg.LockTimeout == 0 {
		cfg.LockTimeout = 30 * time.Second
	}
	if cfg.LockRetryDelay == 0 {
		cfg.LockRetryDelay = 100 * time.Millisecond
	}
	if cfg.LockMaxRetries == 0 {
		cfg.LockMaxRetries = 10
	}
	if cfg.LockRefreshInterval == 0 {
		cfg.LockRefreshInterval = 10 * time.Second
	}
	if cfg.LockCleanupTimeout == 0 {
		cfg.LockCleanupTimeout = 30 * time.Second
	}

	// Set default key management values
	if cfg.KeyPrefix == "" {
		cfg.KeyPrefix = "v2_" // Default to v2_ prefix for new deployments
	}
	if cfg.OldKeyPatterns == nil {
		cfg.OldKeyPatterns = []string{
			"tweet_lock:*", // Old lock pattern without prefix
		}
	}

	// Create Redis client
	client := redis.NewClient(&redis.Options{
		Addr:            fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:        cfg.Password,
		DB:              cfg.DB,
		PoolSize:        cfg.PoolSize,
		MinIdleConns:    cfg.MinIdleConns,
		MaxRetries:      cfg.MaxRetries,
		DialTimeout:     cfg.DialTimeout,
		ReadTimeout:     cfg.ReadTimeout,
		WriteTimeout:    cfg.WriteTimeout,
		PoolTimeout:     cfg.PoolTimeout,
		ConnMaxIdleTime: cfg.IdleTimeout,
		ConnMaxLifetime: cfg.IdleCheckFrequency,
	})

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	// Create distributed lock client
	locker := redislock.New(client)

	log.Info().
		Str("host", cfg.Host).
		Int("port", cfg.Port).
		Int("db", cfg.DB).
		Msg("Successfully connected to Redis")

	return &RedisService{
		client: client,
		locker: locker,
		config: cfg,
	}, nil
}

// Close closes the Redis connection
func (r *RedisService) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// GetClient returns the underlying Redis client
func (r *RedisService) GetClient() *redis.Client {
	return r.client
}

// GetLocker returns the distributed lock client
func (r *RedisService) GetLocker() *redislock.Client {
	return r.locker
}

// GetConfig returns the Redis configuration
func (r *RedisService) GetConfig() config.RedisConfig {
	return r.config
}

// Ping tests the Redis connection
func (r *RedisService) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// CheckRedisEnvironment checks for old keys and optionally cleans them up
func (r *RedisService) CheckRedisEnvironment(ctx context.Context) error {
	if len(r.config.OldKeyPatterns) == 0 {
		log.Debug().Msg("No old key patterns configured, skipping environment check")
		return nil
	}

	log.Info().
		Strs("patterns", r.config.OldKeyPatterns).
		Bool("cleanup_enabled", r.config.CleanupOldKeys).
		Msg("Checking Redis environment for old keys")

	var totalOldKeys int
	for _, pattern := range r.config.OldKeyPatterns {
		keys, err := r.client.Keys(ctx, pattern).Result()
		if err != nil {
			log.Error().
				Err(err).
				Str("pattern", pattern).
				Msg("Failed to check for old keys")
			continue
		}

		if len(keys) > 0 {
			totalOldKeys += len(keys)
			log.Warn().
				Int("count", len(keys)).
				Str("pattern", pattern).
				Strs("sample_keys", getSampleKeys(keys, 5)).
				Msg("Found old keys matching pattern")

			if r.config.CleanupOldKeys {
				if err := r.cleanupKeys(ctx, keys, pattern); err != nil {
					log.Error().
						Err(err).
						Str("pattern", pattern).
						Msg("Failed to cleanup old keys")
					return err
				}
			}
		} else {
			log.Debug().
				Str("pattern", pattern).
				Msg("No old keys found for pattern")
		}
	}

	if totalOldKeys > 0 && !r.config.CleanupOldKeys {
		log.Warn().
			Int("total_old_keys", totalOldKeys).
			Msg("Found old keys but cleanup is disabled. Consider enabling cleanup_old_keys or manually cleaning them")
	}

	return nil
}

// cleanupKeys removes the specified keys from Redis
func (r *RedisService) cleanupKeys(ctx context.Context, keys []string, pattern string) error {
	if len(keys) == 0 {
		return nil
	}

	log.Info().
		Int("count", len(keys)).
		Str("pattern", pattern).
		Msg("Cleaning up old keys")

	deleted, err := r.client.Del(ctx, keys...).Result()
	if err != nil {
		return fmt.Errorf("failed to delete keys: %w", err)
	}

	log.Info().
		Int64("deleted_count", deleted).
		Str("pattern", pattern).
		Msg("Successfully cleaned up old keys")

	return nil
}

// getSampleKeys returns a sample of keys for logging (to avoid huge log messages)
func getSampleKeys(keys []string, maxSample int) []string {
	if len(keys) <= maxSample {
		return keys
	}
	return keys[:maxSample]
}
