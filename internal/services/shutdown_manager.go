package services

import (
	"context"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/rs/zerolog/log"
)

// ShutdownManager handles graceful shutdown of the service
type ShutdownManager struct {
	lockManager     *DistributedLockManager
	shutdownTimeout time.Duration
	shutdownFuncs   []func() error
	shutdownOnce    sync.Once
	shutdownChan    chan os.Signal
	doneChan        chan struct{}
}

// ShutdownFunc represents a function to be called during shutdown
type ShutdownFunc func() error

// NewShutdownManager creates a new shutdown manager
func NewShutdownManager(lockManager *DistributedLockManager, shutdownTimeout time.Duration) *ShutdownManager {
	return &ShutdownManager{
		lockManager:     lockManager,
		shutdownTimeout: shutdownTimeout,
		shutdownFuncs:   make([]func() error, 0),
		shutdownChan:    make(chan os.Signal, 1),
		doneChan:        make(chan struct{}),
	}
}

// RegisterShutdownFunc registers a function to be called during shutdown
// Functions are called in the reverse order they were registered (LIFO)
func (sm *ShutdownManager) RegisterShutdownFunc(fn ShutdownFunc) {
	sm.shutdownFuncs = append(sm.shutdownFuncs, fn)
}

// StartSignalHandler starts listening for shutdown signals
// This method blocks until a shutdown signal is received
func (sm *ShutdownManager) StartSignalHandler() {
	// Register for shutdown signals
	signal.Notify(sm.shutdownChan, syscall.SIGINT, syscall.SIGTERM)
	
	log.Info().Msg("Shutdown manager started, listening for signals...")
	
	// Wait for shutdown signal
	sig := <-sm.shutdownChan
	log.Info().
		Str("signal", sig.String()).
		Msg("Received shutdown signal, initiating graceful shutdown")
	
	// Trigger shutdown
	sm.Shutdown()
}

// Shutdown initiates the graceful shutdown process
func (sm *ShutdownManager) Shutdown() {
	sm.shutdownOnce.Do(func() {
		log.Info().
			Dur("timeout", sm.shutdownTimeout).
			Msg("Starting graceful shutdown process")
		
		// Create context with timeout for shutdown operations
		ctx, cancel := context.WithTimeout(context.Background(), sm.shutdownTimeout)
		defer cancel()
		
		// Execute shutdown in a separate goroutine
		go sm.executeShutdown(ctx)
	})
}

// executeShutdown performs the actual shutdown operations
func (sm *ShutdownManager) executeShutdown(ctx context.Context) {
	defer close(sm.doneChan)
	
	var shutdownErrors []error
	
	// Step 1: Cleanup distributed locks first (highest priority)
	if sm.lockManager != nil {
		log.Info().Msg("Cleaning up distributed locks...")
		if err := sm.lockManager.CleanupAllLocks(ctx); err != nil {
			log.Error().
				Err(err).
				Msg("Error during lock cleanup")
			shutdownErrors = append(shutdownErrors, err)
		} else {
			log.Info().Msg("Lock cleanup completed successfully")
		}
	}
	
	// Step 2: Execute registered shutdown functions in reverse order (LIFO)
	log.Info().
		Int("shutdown_func_count", len(sm.shutdownFuncs)).
		Msg("Executing registered shutdown functions")
	
	for i := len(sm.shutdownFuncs) - 1; i >= 0; i-- {
		select {
		case <-ctx.Done():
			log.Warn().
				Int("remaining_functions", i+1).
				Msg("Shutdown timeout reached, skipping remaining shutdown functions")
			shutdownErrors = append(shutdownErrors, ctx.Err())
			goto done
		default:
			if err := sm.shutdownFuncs[i](); err != nil {
				log.Error().
					Err(err).
					Int("function_index", i).
					Msg("Error executing shutdown function")
				shutdownErrors = append(shutdownErrors, err)
			}
		}
	}
	
done:
	// Log shutdown completion
	if len(shutdownErrors) > 0 {
		log.Error().
			Int("error_count", len(shutdownErrors)).
			Errs("errors", shutdownErrors).
			Msg("Graceful shutdown completed with errors")
	} else {
		log.Info().Msg("Graceful shutdown completed successfully")
	}
}

// Wait blocks until the shutdown process is complete
func (sm *ShutdownManager) Wait() {
	<-sm.doneChan
}

// WaitWithTimeout blocks until the shutdown process is complete or timeout is reached
func (sm *ShutdownManager) WaitWithTimeout(timeout time.Duration) bool {
	select {
	case <-sm.doneChan:
		return true
	case <-time.After(timeout):
		return false
	}
}

// Stop stops the signal handler (for testing purposes)
func (sm *ShutdownManager) Stop() {
	signal.Stop(sm.shutdownChan)
	close(sm.shutdownChan)
}
