package services

import (
	"regexp"
	"strings"
	"time"

	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/ai"
	"real-time-ca-service/internal/db"
	"real-time-ca-service/internal/metrics"
)

// CAService handles contract address extraction and validation
type CAService struct {
	db           *db.Database
	aiService    *ai.Service
	tokenService *TokenService

	// Regular expressions for different chain address formats
	evmRegex    *regexp.Regexp
	solanaRegex *regexp.Regexp
}

// NewCAService creates a new CAService
func NewCAService(database *db.Database, aiService *ai.Service, tokenService *TokenService) *CAService {
	// Compile regular expressions
	evmRegex := regexp.MustCompile(`0x[a-fA-F0-9]{40}`)
	solanaRegex := regexp.MustCompile(`[1-9A-HJ-NP-Za-km-z]{32,44}`)

	return &CAService{
		db:           database,
		aiService:    aiService,
		tokenService: tokenService,
		evmRegex:     evmRegex,
		solanaRegex:  solanaRegex,
	}
}

// ProcessTweet processes the contract addresses already extracted from a tweet
// check if the CA in the tweet is already stored in the Recognized CA table
// update the reference count if it has been deposited
// if not, it is stored once
func (s *CAService) ProcessTweet(tweet *db.Tweet) {
	start := time.Now()

	// Process each address
	for _, tweetCa := range tweet.ExtractedCAs {
		// Determine chain type
		if tweetCa.ChainType == "" {
			log.Warn().
				Str("address", tweetCa.CAAddress).
				Msg("Invalid contract address format")
			continue
		}

		dbStart := time.Now()

		// Gets a list of label names
		var tagNames []string
		// If the tweet has Tag records, extract the tag names
		for _, tag := range tweet.Tags {
			tagNames = append(tagNames, tag.Name)
		}

		// Associate tweet with recognized CA
		if err := s.db.AssociateTweetWithCA(tweetCa.CAAddress, tweetCa.ChainType, tweet, tagNames); err != nil {
			log.Error().
				Err(err).
				Str("tweet_id", tweet.TweetID).
				Str("address", tweetCa.CAAddress).
				Str("chain_type", tweetCa.ChainType).
				Msg("Error associating tweet with CA")
			metrics.DatabaseOperationsTotal.WithLabelValues("save", "recognized_cas", "error").Inc()
			continue
		}
		metrics.DatabaseOperationsTotal.WithLabelValues("save", "recognized_cas", "success").Inc()
		metrics.DatabaseOperationDuration.WithLabelValues("save", "recognized_cas").Observe(time.Since(dbStart).Seconds())
		// Increment counter for extracted addresses
		metrics.ContractAddressesExtracted.Inc()

		dbStart = time.Now()
		// Check if the contract address is already in the RECOGNIZEDCA table and update the reference count
		err := s.db.IncrementCAReference(tweetCa.CAAddress, tweetCa.ChainType, "", tweet.PublishedAt, tagNames)
		if err != nil {
			log.Error().
				Err(err).
				Str("address", tweetCa.CAAddress).
				Msg("Error updating reference count for CA")
			metrics.DatabaseOperationsTotal.WithLabelValues("update", "recognized_cas", "error").Inc()
			continue
		}
		metrics.DatabaseOperationsTotal.WithLabelValues("update", "recognized_cas", "success").Inc()
		metrics.DatabaseOperationDuration.WithLabelValues("update", "recognized_cas").Observe(time.Since(dbStart).Seconds())

		// Added counter for identified contract addresses
		metrics.RecognizedContractAddressesFound.Inc()

		log.Info().
			Str("address", tweetCa.CAAddress).
			Str("chain_type", tweetCa.ChainType).
			Str("tweet_id", tweet.TweetID).
			Msg("Contract address processed and reference count updated")

		s.tokenService.UpdateTokenDetails(&tweetCa)
	}

	log.Debug().
		Str("tweet_id", tweet.TweetID).
		Dur("duration", time.Since(start)).
		Msg("Finished processing tweet for contract addresses")
}

// extractAddresses extracts potential contract addresses from text
func (s *CAService) extractAddresses(text string) []string {
	// Find all potential addresses
	evmAddresses := s.evmRegex.FindAllString(text, -1)
	solanaAddresses := s.solanaRegex.FindAllString(text, -1)

	// Combine results
	addresses := append(evmAddresses, solanaAddresses...)

	// Remove duplicates
	uniqueAddresses := make(map[string]bool)
	for _, addr := range addresses {
		uniqueAddresses[addr] = true
	}

	// Convert back to slice
	result := make([]string, 0, len(uniqueAddresses))
	for addr := range uniqueAddresses {
		result = append(result, addr)
	}

	return result
}

// detectChainType determines the chain type of an address
func (s *CAService) detectChainType(address string) string {
	// Check for evm address
	if s.evmRegex.MatchString(address) {
		// Validate EIP-55 checksum for evm addresses
		if s.validateEIP55Checksum(address) {
			return "evm"
		}
	}

	// Check for solana address
	if s.solanaRegex.MatchString(address) {
		return "solana"
	}

	return ""
}

// validateEIP55Checksum validates an evm address according to EIP-55
// This is a simplified implementation - in production, use a proper library
func (s *CAService) validateEIP55Checksum(address string) bool {
	// For simplicity, we'll just check if the address is all lowercase or all uppercase
	// In a real implementation, we would validate the checksum properly

	// If the address is all lowercase or all uppercase, it's not a valid EIP-55 address
	// unless it's all digits and lowercase a-f
	if address == strings.ToLower(address) || address == strings.ToUpper(address) {
		// Check if it's all digits and lowercase a-f
		if address == strings.ToLower(address) {
			// This is acceptable for addresses without any letters
			hasLetters := false
			for _, c := range address[2:] { // Skip "0x"
				if c >= 'a' && c <= 'f' {
					hasLetters = true
					break
				}
			}

			// If it has letters and is all lowercase, it's not EIP-55 compliant
			if hasLetters {
				return false
			}
		} else {
			// All uppercase is not EIP-55 compliant
			return false
		}
	}

	// In a real implementation, we would calculate the checksum here
	// For now, we'll assume it's valid if it has mixed case
	return true
}

// AddRecognizedCA adds a new recognized CA
func (s *CAService) AddRecognizedCA(caAddress, chainID, tokenNameHint string) error {
	recognizedCA := &db.RecognizedCA{
		CAAddress:     caAddress,
		ChainType:     chainID,
		TokenNameHint: tokenNameHint,
		AddedAt:       time.Now(),
	}

	return s.db.SaveRecognizedCA(recognizedCA)
}

// DeleteRecognizedCA deletes a recognized CA
func (s *CAService) DeleteRecognizedCA(caAddress string) error {
	return s.db.DeleteRecognizedCA(caAddress)
}

// GetRecognizedCAsForUpdate gets recognized CAs that need token data update
func (s *CAService) GetRecognizedCAsForUpdate(limit int) ([]*db.RecognizedCA, error) {
	return s.db.GetRecognizedCAsForUpdate(limit)
}

// GetRecognizedCAs retrieves recognized CAs with their associated tweets
func (s *CAService) GetRecognizedCAs(limit, offset int) ([]*db.RecognizedCA, error) {
	return s.db.GetRecognizedCAsWithTweets(limit, offset)
}

// GetRecognizedCAsByTags retrieves recognized CAs with their associated tweets, filtered by tags
// If tags is empty, it returns all CAs (same as GetRecognizedCAs)
func (s *CAService) GetRecognizedCAsByTags(limit, offset int, tags []string) ([]*db.RecognizedCA, error) {
	return s.db.GetRecognizedCAsByTags(limit, offset, tags)
}

// GetRecognizedCAByAddressAndChainID retrieves a single recognized CA by address and chain ID
func (s *CAService) GetRecognizedCAByAddressAndChainID(caAddress, chainID string) (*db.RecognizedCA, error) {
	return s.db.GetRecognizedCAByAddressAndChainID(caAddress, chainID)
}
