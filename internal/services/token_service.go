package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/db"
	"real-time-ca-service/internal/metrics"
	"real-time-ca-service/internal/utils"

	"github.com/rs/zerolog/log"
)

// TokenService handles interactions with the DexScreener API
type TokenService struct {
	config        config.DexScreenerConfig
	moralisConfig config.MoralisConfig
	db            *db.Database
	caService     *CAService
	httpClient    *http.Client

	// For controlling the token updater goroutine
	stopChan chan struct{}
	wg       sync.WaitGroup

	// For rate limiting
	rateLimiter        *RateLimiter
	moralisRateLimiter *RateLimiter
}

// DexScreenerResponse represents the response from the DexScreener API
type DexScreenerResponse struct {
	ChainId     string    `json:"chainId"`
	DexId       string    `json:"dexId"`
	Url         string    `json:"url"`
	PairAddress string    `json:"pairAddress"`
	Labels      []string  `json:"labels"`
	BaseToken   BaseToken `json:"baseToken"`
	QuoteToken  struct {
		Address string `json:"address"`
		Name    string `json:"name"`
		Symbol  string `json:"symbol"`
	} `json:"quoteToken"`
	PriceNative string `json:"priceNative"`
	PriceUsd    string `json:"priceUsd"`
	Txns        struct {
		ANYADDITIONALPROPERTY struct {
			Buys  float64 `json:"buys"`
			Sells float64 `json:"sells"`
		} `json:"ANY_ADDITIONAL_PROPERTY"`
	} `json:"txns"`
	Volume struct {
		ANYADDITIONALPROPERTY float64 `json:"ANY_ADDITIONAL_PROPERTY"`
	} `json:"volume"`
	PriceChange struct {
		ANYADDITIONALPROPERTY float64 `json:"ANY_ADDITIONAL_PROPERTY"`
	} `json:"priceChange"`
	Liquidity     Liquidity `json:"liquidity"`
	Fdv           float64   `json:"fdv"`
	MarketCap     float64   `json:"marketCap"`
	PairCreatedAt int64     `json:"pairCreatedAt"`
	Info          TokenInfo `json:"info"`
	Boosts        struct {
		Active int `json:"active"`
	} `json:"boosts"`
}

// Liquidity represents liquidity information
type Liquidity struct {
	Usd   float64 `json:"usd"`
	Base  float64 `json:"base"`
	Quote float64 `json:"quote"`
}

// BaseToken represents the base token in a pair
type BaseToken struct {
	Address string `json:"address"`
	Name    string `json:"name"`
	Symbol  string `json:"symbol"`
}

// TokenInfo represents additional token information
type TokenInfo struct {
	ImageUrl string `json:"imageUrl"`
	Websites []struct {
		Url string `json:"url"`
	} `json:"websites"`
	Socials []SocialLink `json:"socials"`
}

// SocialLink represents a social media link
// The JSON Tag is correct, as are the type and URL
type SocialLink struct {
	Platform string `json:"type"`
	URL      string `json:"url"`
}

// MoralisEVMTokenHolderResponse represents the response from the Moralis EVM token holder stats API
type MoralisEVMTokenHolderResponse struct {
	TotalHolders int64 `json:"totalHolders"`
}

// MoralisSolanaTokenHolderResponse represents the response from the Moralis Solana token holder stats API
type MoralisSolanaTokenHolderResponse struct {
	TotalHolders int64 `json:"totalHolders"`
}

// MoralisEVMTokenMetadataResponse represents the response from the Moralis EVM token metadata API
type MoralisEVMTokenMetadataResponse struct {
	Address     string  `json:"address"`
	Name        string  `json:"name"`
	Symbol      string  `json:"symbol"`
	Decimals    string  `json:"decimals"`
	Logo        string  `json:"logo"`
	LogoHash    string  `json:"logo_hash"`
	Thumbnail   string  `json:"thumbnail"`
	TotalSupply string  `json:"total_supply"`
	CreatedAt   *string `json:"created_at"`
	MarketCap   string  `json:"market_cap"`
	Links       struct {
		Discord   string `json:"discord"`
		Medium    string `json:"medium"`
		Reddit    string `json:"reddit"`
		Telegram  string `json:"telegram"`
		Twitter   string `json:"twitter"`
		Website   string `json:"website"`
		GitHub    string `json:"github"`
		Bitbucket string `json:"bitbucket"`
		Facebook  string `json:"facebook"`
		Instagram string `json:"instagram"`
		LinkedIn  string `json:"linkedin"`
		TikTok    string `json:"tiktok"`
		YouTube   string `json:"youtube"`
	} `json:"links"`
}

// MoralisEVMTokenPriceResponse represents the response from the Moralis EVM token price API
type MoralisEVMTokenPriceResponse struct {
	TokenName             string  `json:"tokenName"`
	TokenSymbol           string  `json:"tokenSymbol"`
	TokenLogo             string  `json:"tokenLogo"`
	TokenDecimals         string  `json:"tokenDecimals"`
	UsdPrice              float64 `json:"usdPrice"`
	UsdPriceFormatted     string  `json:"usdPriceFormatted"`
	Change24h             string  `json:"24hrPercentChange"`
	ExchangeAddress       string  `json:"exchangeAddress"`
	ExchangeName          string  `json:"exchangeName"`
	TokenAddress          string  `json:"tokenAddress"`
	ToBlock               string  `json:"toBlock"`
	PossibleSpam          bool    `json:"possibleSpam"`
	VerifiedContract      bool    `json:"verifiedContract"`
	PairAddress           string  `json:"pairAddress"`
	PairTotalLiquidityUsd string  `json:"pairTotalLiquidityUsd"`
}

// MoralisSolanaTokenMetadataResponse represents the response from the Moralis Solana token metadata API
type MoralisSolanaTokenMetadataResponse struct {
	Mint     string `json:"mint"`
	Standard string `json:"standard"`
	Name     string `json:"name"`
	Symbol   string `json:"symbol"`
	Logo     string `json:"logo"`
	Decimals string `json:"decimals"`
	Metaplex struct {
		MetadataUri          string `json:"metadataUri"`
		MasterEdition        bool   `json:"masterEdition"`
		IsMutable            bool   `json:"isMutable"`
		SellerFeeBasisPoints int    `json:"sellerFeeBasisPoints"`
		UpdateAuthority      string `json:"updateAuthority"`
		PrimarySaleHappened  int    `json:"primarySaleHappened"`
	} `json:"metaplex"`
	FullyDilutedValue    string `json:"fullyDilutedValue"`
	TotalSupply          string `json:"totalSupply"`
	TotalSupplyFormatted string `json:"totalSupplyFormatted"`
	Links                struct {
		Moralis string `json:"moralis"`
		Twitter string `json:"twitter"`
	} `json:"links"`
	Description        string `json:"description"`
	IsVerifiedContract bool   `json:"isVerifiedContract"`
	PossibleSpam       bool   `json:"possibleSpam"`
}

// MoralisSolanaTokenPriceResponse represents the response from the Moralis Solana token price API
type MoralisSolanaTokenPriceResponse struct {
	NativePrice struct {
		Value    string `json:"value"`
		Decimals int    `json:"decimals"`
		Name     string `json:"name"`
		Symbol   string `json:"symbol"`
	} `json:"nativePrice"`
	UsdPrice           float64 `json:"usdPrice"`
	ExchangeAddress    string  `json:"exchangeAddress"`
	ExchangeName       string  `json:"exchangeName"`
	TokenAddress       string  `json:"tokenAddress"`
	PairAddress        string  `json:"pairAddress"`
	Logo               string  `json:"logo"`
	Name               string  `json:"name"`
	Symbol             string  `json:"symbol"`
	IsVerifiedContract bool    `json:"isVerifiedContract"`
	UsdPrice24h        float64 `json:"usdPrice24h"`
}

// MoralisSolanaTokenAnalyticsResponse represents the response from the Moralis Solana token analytics API
type MoralisSolanaTokenAnalyticsResponse struct {
	TokenAddress               string `json:"tokenAddress"`
	TotalLiquidityUsd          string `json:"totalLiquidityUsd"`
	TotalFullyDilutedValuation string `json:"totalFullyDilutedValuation"`
}

// NewTokenService creates a new TokenService
func NewTokenService(dexConfig config.DexScreenerConfig, moralisConfig config.MoralisConfig, database *db.Database) *TokenService {
	return &TokenService{
		config:             dexConfig,
		moralisConfig:      moralisConfig,
		db:                 database,
		httpClient:         &http.Client{Timeout: 10 * time.Second},
		stopChan:           make(chan struct{}),
		rateLimiter:        NewRateLimiter(dexConfig.RequestsPerSec, time.Second),
		moralisRateLimiter: NewRateLimiter(moralisConfig.RequestsPerSec, time.Second),
	}
}

// SetCAService sets the CAService
func (s *TokenService) SetCAService(caService *CAService) {
	s.caService = caService
}

// StartTokenUpdater starts the token updater process
func (s *TokenService) StartTokenUpdater() {
	// Update recognized CAs addresses
	// s.updateRecognizedCAAddresses()

	s.wg.Add(1)
	go s.updateTokens()
}

// updateRecognizedCAAddresses updates the ca_address for recognized CAs
// For EVM chains, it converts addresses to checksum format
// For Solana chains, it updates addresses from DexScreener data
func (s *TokenService) updateRecognizedCAAddresses() {
	// Get all recognized CAs
	var recognizedCAs []db.RecognizedCA
	if err := s.db.DB.Find(&recognizedCAs).Error; err != nil {
		log.Error().Err(err).Msg("Failed to fetch recognized CAs for address update")
		return
	}

	log.Info().Int("count", len(recognizedCAs)).Msg("Updating recognized CA addresses")

	// Process each CA
	for _, ca := range recognizedCAs {
		var newAddress string
		var err error

		// Process based on chain type
		switch ca.ChainType {
		case "evm":
			// Convert to checksum address
			newAddress, err = utils.ToChecksumAddress(ca.CAAddress)
			if err != nil {
				log.Warn().
					Err(err).
					Str("ca_address", ca.CAAddress).
					Str("chain_type", ca.ChainType).
					Msg("Failed to convert EVM address to checksum format")
				continue
			}

			// Skip if address hasn't changed
			if newAddress == ca.CAAddress {
				continue
			}

			log.Info().
				Str("old_address", ca.CAAddress).
				Str("new_address", newAddress).
				Msg("Updating EVM address to checksum format")

		case "solana":
			// Fetch pair data from DexScreener
			resp, err := s.fetchDexScreenerPairData(ca.CAAddress, "solana")
			if err != nil {
				log.Warn().
					Err(err).
					Str("ca_address", ca.CAAddress).
					Msg("Failed to fetch Solana token data from DexScreener")
				continue
			}

			// Get the base token address
			newAddress = resp.BaseToken.Address

			// Skip if address hasn't changed
			if newAddress == ca.CAAddress {
				continue
			}

			log.Info().
				Str("old_address", ca.CAAddress).
				Str("new_address", newAddress).
				Msg("Updating Solana address from DexScreener data")

		default:
			// Skip unknown chain types
			log.Warn().
				Str("chain_type", ca.ChainType).
				Str("ca_address", ca.CAAddress).
				Msg("Skipping address update for unknown chain type")
			continue
		}

		// Update the address in the database
		if err := s.db.DB.Model(&db.RecognizedCA{}).
			Where("ca_address = ?", ca.CAAddress).
			Update("ca_address", newAddress).Error; err != nil {
			log.Error().
				Err(err).
				Str("old_address", ca.CAAddress).
				Str("new_address", newAddress).
				Msg("Failed to update CA address in database")
		}
	}
}

// StopTokenUpdater stops the token updater process
func (s *TokenService) StopTokenUpdater() {
	close(s.stopChan)
	s.wg.Wait()
}

// updateTokens continuously updates token information
func (s *TokenService) updateTokens() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.config.UpdateInterval)
	defer ticker.Stop()

	// Initial update
	s.processTokenUpdates()

	for {
		select {
		case <-ticker.C:
			s.processTokenUpdates()
		case <-s.stopChan:
			log.Print("Stopping token updater")
			return
		}
	}
}

// processTokenUpdates processes token updates
func (s *TokenService) processTokenUpdates() {
	log.Print("Processing token updates...")

	// Get recognized CAs that need updating
	recognizedCAs, err := s.caService.GetRecognizedCAsForUpdate(1000)
	if err != nil {
		log.Printf("Error getting recognized CAs for update: %v", err)
		return
	}

	if len(recognizedCAs) == 0 {
		log.Print("No tokens to update")
		return
	} else {
		// Process each CA
		for _, ca := range recognizedCAs {
			s.UpdateTokenDetails(ca)
		}
	}

	// s.processTokenUpdates()
}

// UpdateTokenDetails updates token details for a recognized CA
func (s *TokenService) UpdateTokenDetails(ca *db.RecognizedCA) {
	start := time.Now()

	if start.Sub(ca.LastTweetAt) > 48*time.Hour {
		log.Debug().
			Str("address", ca.CAAddress).
			Str("chain_type", ca.ChainType).
			Msg("Skipping token update for old CA")
		return
	}

	log.Debug().
		Str("address", ca.CAAddress).
		Str("chain_type", ca.ChainType).
		Msg("Updating token details")

	// Handle different chain types with different data sources
	if ca.ChainType == "solana" {
		s.processSolanaTokenDetails(ca.CAAddress, start)
	} else if ca.ChainType == "evm" {
		// For EVM, try multiple chains
		chains := []string{"base", "bsc", "ethereum"}
		for _, chain := range chains {
			s.processEVMTokenDetails(ca.CAAddress, chain, start)
		}
	} else {
		// For other specific EVM chains
		// s.processEVMTokenDetails(ca.CAAddress, ca.ChainType, start)
	}
}

// processEVMTokenDetails processes EVM token details using Moralis APIs
func (s *TokenService) processEVMTokenDetails(caAddress, chainID string, start time.Time) {
	log.Debug().
		Str("address", caAddress).
		Str("chain_id", chainID).
		Msg("Processing EVM token details from Moralis")

	// Get existing token details to preserve data
	var existTokenDetail db.TokenDetails
	_ = s.db.
		Where("ca_address_fk = ?", caAddress).
		Where("chain_id = ?", chainID).
		First(&existTokenDetail).Error

	// Fetch token metadata from Moralis
	metadata, err := s.fetchEVMTokenMetadata(caAddress, chainID)
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Str("chain_id", chainID).
			Msg("Error fetching EVM token metadata from Moralis")
	}

	// Fetch token price from Moralis
	priceData, err := s.fetchEVMTokenPrice(caAddress, chainID)
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Str("chain_id", chainID).
			Msg("Error fetching EVM token price from Moralis")
	}

	// Fetch token holder count from Moralis
	holderCount, err := s.fetchEVMTokenHolderCount(caAddress, chainID)
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Str("chain_id", chainID).
			Msg("Error fetching EVM token holder count from Moralis")
	}

	// Fetch DexScreener data as backup/supplement
	dexPairData, err := s.fetchDexScreenerPairData(caAddress, chainID)
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Str("chain_id", chainID).
			Msg("Error fetching DexScreener pair data as supplement")
	}

	// If all critical data sources failed and we have no existing data, skip processing
	if metadata == nil && priceData == nil && dexPairData == nil && existTokenDetail.ID == 0 {
		log.Warn().
			Str("address", caAddress).
			Str("chain_id", chainID).
			Msg("All data sources failed and no existing data - skipping token processing")
		return
	}

	// Extract and process data from all sources
	var twitterURL string
	var tokenCreatedAt *time.Time
	var pairCreatedAt *time.Time
	var marketCapUSD float64
	var priceUSD float64
	var tokenName, symbol, logoURL string

	// Process Moralis metadata (priority)
	if metadata != nil {
		if metadata.Links.Twitter != "" {
			twitterURL = metadata.Links.Twitter
		}
		if metadata.CreatedAt != nil {
			createdAt, err := time.Parse(time.RFC3339, *metadata.CreatedAt)
			if err == nil {
				tokenCreatedAt = &createdAt
			}
		}
		if metadata.MarketCap != "" {
			marketCap, err := strconv.ParseFloat(metadata.MarketCap, 64)
			if err == nil {
				marketCapUSD = marketCap
			}
		}
		if metadata.Name != "" {
			tokenName = metadata.Name
		}
		if metadata.Symbol != "" {
			symbol = metadata.Symbol
		}
		if metadata.Logo != "" {
			logoURL = metadata.Logo
		}
	}

	// Process Moralis price data (priority)
	if priceData != nil && priceData.UsdPrice > 0 {
		priceUSD = priceData.UsdPrice
	}

	// Use DexScreener data as supplement where Moralis data is missing
	if dexPairData != nil {
		// Use DexScreener pair creation time
		pairTime := time.Unix(dexPairData.PairCreatedAt/1000, 0)
		pairCreatedAt = &pairTime

		// Supplement missing data from DexScreener
		if tokenName == "" && dexPairData.BaseToken.Name != "" {
			tokenName = dexPairData.BaseToken.Name
		}
		if symbol == "" && dexPairData.BaseToken.Symbol != "" {
			symbol = dexPairData.BaseToken.Symbol
		}
		if logoURL == "" && dexPairData.Info.ImageUrl != "" {
			logoURL = dexPairData.Info.ImageUrl
		}
		if twitterURL == "" {
			// Extract Twitter URL from DexScreener socials
			for _, social := range dexPairData.Info.Socials {
				if strings.ToLower(social.Platform) == "twitter" {
					twitterURL = social.URL
					break
				}
			}
		}
		if priceUSD == 0 && dexPairData.PriceUsd != "" {
			price, parseErr := strconv.ParseFloat(dexPairData.PriceUsd, 64)
			if parseErr == nil {
				priceUSD = price
			}
		}
		if marketCapUSD == 0 && dexPairData.Fdv > 0 {
			marketCapUSD = dexPairData.Fdv
		}
	}

	// Create token details from combined data
	tokenDetails := &db.TokenDetails{
		CAAddressFK:    caAddress,
		ChainID:        chainID,
		Source:         "moralis+dexscreener", // Indicate combined sources
		LastUpdatedAt:  time.Now(),
		PairCreatedAt:  pairCreatedAt,
		TokenCreatedAt: tokenCreatedAt,
	}

	// Only update fields that have valid data, preserve existing data for empty fields
	if tokenName != "" {
		tokenDetails.TokenName = tokenName
	} else if existTokenDetail.TokenName != "" {
		tokenDetails.TokenName = existTokenDetail.TokenName
	}

	if symbol != "" {
		tokenDetails.Symbol = symbol
	} else if existTokenDetail.Symbol != "" {
		tokenDetails.Symbol = existTokenDetail.Symbol
	}

	if logoURL != "" {
		tokenDetails.TokenLogoURL = logoURL
	} else if existTokenDetail.TokenLogoURL != "" {
		tokenDetails.TokenLogoURL = existTokenDetail.TokenLogoURL
	}

	if twitterURL != "" {
		tokenDetails.TokenTwitterURL = twitterURL
	} else if existTokenDetail.TokenTwitterURL != "" {
		tokenDetails.TokenTwitterURL = existTokenDetail.TokenTwitterURL
	}

	// Set price and market cap (prefer new data but preserve existing if new is invalid)
	if priceUSD > 0 {
		tokenDetails.PriceUSD = &priceUSD
	} else if existTokenDetail.PriceUSD != nil && *existTokenDetail.PriceUSD > 0 {
		tokenDetails.PriceUSD = existTokenDetail.PriceUSD
	}

	if marketCapUSD > 0 {
		tokenDetails.MarketCapUSD = &marketCapUSD
	} else if existTokenDetail.MarketCapUSD != nil && *existTokenDetail.MarketCapUSD > 0 {
		tokenDetails.MarketCapUSD = existTokenDetail.MarketCapUSD
	}

	// Set holder count (prefer new data but preserve existing if new is invalid)
	if holderCount != nil && *holderCount > 0 {
		tokenDetails.HolderCount = holderCount
	} else if existTokenDetail.HolderCount != nil && *existTokenDetail.HolderCount > 0 {
		tokenDetails.HolderCount = existTokenDetail.HolderCount
	}

	// Preserve existing pair creation time if new one is not available
	if tokenDetails.PairCreatedAt == nil && existTokenDetail.PairCreatedAt != nil {
		tokenDetails.PairCreatedAt = existTokenDetail.PairCreatedAt
	}

	// Preserve existing token creation time if new one is not available
	if tokenDetails.TokenCreatedAt == nil && existTokenDetail.TokenCreatedAt != nil {
		tokenDetails.TokenCreatedAt = existTokenDetail.TokenCreatedAt
	}

	// Skip saving if we don't have any meaningful data
	if tokenDetails.TokenName == "" && tokenDetails.Symbol == "" &&
		tokenDetails.PriceUSD == nil && tokenDetails.MarketCapUSD == nil &&
		tokenDetails.HolderCount == nil {
		log.Warn().
			Str("address", caAddress).
			Str("chain_id", chainID).
			Msg("Skipping save: no meaningful token data available from any source")
		return
	}

	// Save to database
	dbStart := time.Now()
	if err := s.db.SaveTokenDetails(tokenDetails); err != nil {
		log.Error().
			Err(err).
			Str("address", caAddress).
			Str("chain_id", chainID).
			Msg("Error saving EVM token details")
		metrics.DatabaseOperationsTotal.WithLabelValues("save", "token_details", "error").Inc()
		return
	}
	metrics.DatabaseOperationsTotal.WithLabelValues("save", "token_details", "success").Inc()
	metrics.DatabaseOperationDuration.WithLabelValues("save", "token_details").Observe(time.Since(dbStart).Seconds())

	// Increment token details updated counter
	metrics.TokenDetailsUpdated.Inc()

	logEvent := log.Info().
		Str("address", caAddress).
		Str("chain_id", chainID).
		Dur("duration", time.Since(start))

	if tokenDetails.TokenName != "" {
		logEvent = logEvent.Str("token_name", tokenDetails.TokenName)
	}
	if tokenDetails.Symbol != "" {
		logEvent = logEvent.Str("symbol", tokenDetails.Symbol)
	}
	if tokenDetails.PriceUSD != nil {
		logEvent = logEvent.Float64("price_usd", *tokenDetails.PriceUSD)
	}
	if tokenDetails.HolderCount != nil {
		logEvent = logEvent.Int64("holder_count", *tokenDetails.HolderCount)
	}

	// Log data sources used
	moralisDataCount := 0
	dexScreenerDataCount := 0
	if metadata != nil {
		moralisDataCount++
	}
	if priceData != nil {
		moralisDataCount++
	}
	if dexPairData != nil {
		dexScreenerDataCount++
	}

	logEvent = logEvent.
		Int("moralis_apis_used", moralisDataCount).
		Int("dexscreener_apis_used", dexScreenerDataCount)

	// Add data quality indicators
	dataQualityScore := 0
	if tokenDetails.TokenName != "" {
		dataQualityScore++
	}
	if tokenDetails.Symbol != "" {
		dataQualityScore++
	}
	if tokenDetails.PriceUSD != nil {
		dataQualityScore++
	}
	if tokenDetails.MarketCapUSD != nil {
		dataQualityScore++
	}
	if tokenDetails.HolderCount != nil {
		dataQualityScore++
	}
	if tokenDetails.TokenLogoURL != "" {
		dataQualityScore++
	}
	if tokenDetails.TokenTwitterURL != "" {
		dataQualityScore++
	}

	logEvent = logEvent.
		Int("data_quality_score", dataQualityScore).
		Int("max_data_fields", 7).
		Bool("has_basic_info", tokenDetails.TokenName != "" && tokenDetails.Symbol != "").
		Bool("has_price_data", tokenDetails.PriceUSD != nil).
		Bool("used_existing_data", existTokenDetail.ID != 0)

	logEvent.Msg("Successfully updated EVM token details with combined data sources")
}

// processSolanaTokenDetails processes Solana token details using Moralis and DexScreener APIs
func (s *TokenService) processSolanaTokenDetails(caAddress string, start time.Time) {
	log.Debug().
		Str("address", caAddress).
		Msg("Processing Solana token details from Moralis and DexScreener")

	// Get existing token details to preserve data
	var existTokenDetail db.TokenDetails
	_ = s.db.
		Where("ca_address_fk = ?", caAddress).
		Where("chain_id = ?", "solana").
		First(&existTokenDetail).Error

	// Fetch token metadata from Moralis (priority)
	metadata, err := s.fetchSolanaTokenMetadata(caAddress)
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Msg("Error fetching Solana token metadata from Moralis")
	}

	// Fetch token analytics from Moralis for market cap and price
	analytics, err := s.fetchSolanaTokenAnalytics(caAddress)
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Msg("Error fetching Solana token analytics from Moralis")
	}

	// Fetch token holder count from Moralis
	holderCount, err := s.fetchSolanaTokenHolderCount(caAddress)
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Msg("Error fetching Solana token holder count from Moralis")
	}

	// Fetch DexScreener data as backup/supplement (especially for pair creation time)
	dexPairData, err := s.fetchDexScreenerPairData(caAddress, "solana")
	if err != nil {
		log.Warn().
			Err(err).
			Str("address", caAddress).
			Msg("Error fetching DexScreener pair data as supplement")
	}

	// If all critical data sources failed and we have no existing data, skip processing
	if metadata == nil && analytics == nil && dexPairData == nil && existTokenDetail.ID == 0 {
		log.Warn().
			Str("address", caAddress).
			Msg("All data sources failed and no existing data - skipping token processing")
		return
	}

	// Extract and process data from all sources
	var twitterURL string
	var tokenCreatedAt *time.Time
	var pairCreatedAt *time.Time
	var marketCapUSD float64
	var priceUSD float64
	var tokenName, symbol, logoURL string

	// Process Moralis metadata (priority)
	if metadata != nil {
		if metadata.Name != "" {
			tokenName = metadata.Name
		}
		if metadata.Symbol != "" {
			symbol = metadata.Symbol
		}
		if metadata.Logo != "" {
			logoURL = metadata.Logo
		}
		if metadata.Links.Twitter != "" {
			twitterURL = metadata.Links.Twitter
		}
	}

	// Process Moralis analytics (priority for price and market cap)
	if analytics != nil {
		if analytics.TotalFullyDilutedValuation != "" {
			marketCap, parseErr := strconv.ParseFloat(analytics.TotalFullyDilutedValuation, 64)
			if parseErr == nil {
				marketCapUSD = marketCap
			}
		}
		// Note: TotalLiquidityUsd is not actually the price, let's use DexScreener for price
	}

	// Use DexScreener data as supplement where Moralis data is missing
	if dexPairData != nil {
		// Always use DexScreener pair creation time (required by business logic)
		pairTime := time.Unix(dexPairData.PairCreatedAt/1000, 0)
		pairCreatedAt = &pairTime
		tokenCreatedAt = &pairTime

		// Supplement missing data from DexScreener
		if tokenName == "" && dexPairData.BaseToken.Name != "" {
			tokenName = dexPairData.BaseToken.Name
		}
		if symbol == "" && dexPairData.BaseToken.Symbol != "" {
			symbol = dexPairData.BaseToken.Symbol
		}
		if dexPairData.Info.ImageUrl != "" {
			logoURL = dexPairData.Info.ImageUrl
		}

		// Extract Twitter URL from DexScreener socials
		for _, social := range dexPairData.Info.Socials {
			if strings.ToLower(social.Platform) == "twitter" {
				twitterURL = social.URL
				break
			}
		}

		// Use DexScreener price if Moralis analytics doesn't have it
		if dexPairData.PriceUsd != "" {
			price, parseErr := strconv.ParseFloat(dexPairData.PriceUsd, 64)
			if parseErr == nil {
				priceUSD = price
			}
		}

		// Use DexScreener market cap if Moralis doesn't have it
		if marketCapUSD == 0 && dexPairData.Fdv > 0 {
			marketCapUSD = dexPairData.Fdv
		}
	}

	// Create token details from combined data
	tokenDetails := &db.TokenDetails{
		CAAddressFK:    caAddress,
		ChainID:        "solana",
		Source:         "moralis+dexscreener", // Indicate combined sources
		LastUpdatedAt:  time.Now(),
		PairCreatedAt:  pairCreatedAt,
		TokenCreatedAt: tokenCreatedAt,
	}

	// Only update fields that have valid data, preserve existing data for empty fields
	if tokenName != "" {
		tokenDetails.TokenName = tokenName
	} else if existTokenDetail.TokenName != "" {
		tokenDetails.TokenName = existTokenDetail.TokenName
	}

	if symbol != "" {
		tokenDetails.Symbol = symbol
	} else if existTokenDetail.Symbol != "" {
		tokenDetails.Symbol = existTokenDetail.Symbol
	}

	if logoURL != "" {
		tokenDetails.TokenLogoURL = logoURL
	} else if existTokenDetail.TokenLogoURL != "" {
		tokenDetails.TokenLogoURL = existTokenDetail.TokenLogoURL
	}

	if twitterURL != "" {
		tokenDetails.TokenTwitterURL = twitterURL
	} else if existTokenDetail.TokenTwitterURL != "" {
		tokenDetails.TokenTwitterURL = existTokenDetail.TokenTwitterURL
	}

	// Set price and market cap (prefer new data but preserve existing if new is invalid)
	if priceUSD > 0 {
		tokenDetails.PriceUSD = &priceUSD
	} else {
		priceData, err := s.fetchSolanaTokenPrice(caAddress)
		if err != nil {
			log.Warn().
				Err(err).
				Str("address", caAddress).
				Msg("Error fetching Solana token price from Moralis as fallback")
		} else if priceData != nil && priceData.UsdPrice > 0 {
			priceUSD = priceData.UsdPrice
			tokenDetails.PriceUSD = &priceUSD
			log.Debug().
				Str("address", caAddress).
				Float64("price_usd", priceUSD).
				Str("exchange", priceData.ExchangeName).
				Msg("Successfully fetched price from Moralis token price API as fallback")
		}
	}

	if marketCapUSD > 0 {
		tokenDetails.MarketCapUSD = &marketCapUSD
	} else if existTokenDetail.MarketCapUSD != nil && *existTokenDetail.MarketCapUSD > 0 {
		tokenDetails.MarketCapUSD = existTokenDetail.MarketCapUSD
	}

	// Set holder count (prefer new data but preserve existing if new is invalid)
	if holderCount != nil && *holderCount > 0 {
		tokenDetails.HolderCount = holderCount
	} else if existTokenDetail.HolderCount != nil && *existTokenDetail.HolderCount > 0 {
		tokenDetails.HolderCount = existTokenDetail.HolderCount
	}

	// Preserve existing pair creation time if new one is not available
	if tokenDetails.PairCreatedAt == nil && existTokenDetail.PairCreatedAt != nil {
		tokenDetails.PairCreatedAt = existTokenDetail.PairCreatedAt
	}

	// Preserve existing token creation time if new one is not available
	if tokenDetails.TokenCreatedAt == nil && existTokenDetail.TokenCreatedAt != nil {
		tokenDetails.TokenCreatedAt = existTokenDetail.TokenCreatedAt
	}

	// Skip saving if we don't have any meaningful data
	if tokenDetails.TokenName == "" && tokenDetails.Symbol == "" &&
		tokenDetails.PriceUSD == nil && tokenDetails.MarketCapUSD == nil &&
		tokenDetails.HolderCount == nil {
		log.Warn().
			Str("address", caAddress).
			Msg("Skipping save: no meaningful token data available from any source")
		return
	}

	// Save to database
	dbStart := time.Now()
	if err := s.db.SaveTokenDetails(tokenDetails); err != nil {
		log.Error().
			Err(err).
			Str("address", caAddress).
			Msg("Error saving Solana token details")
		metrics.DatabaseOperationsTotal.WithLabelValues("save", "token_details", "error").Inc()
		return
	}
	metrics.DatabaseOperationsTotal.WithLabelValues("save", "token_details", "success").Inc()
	metrics.DatabaseOperationDuration.WithLabelValues("save", "token_details").Observe(time.Since(dbStart).Seconds())

	// Increment token details updated counter
	metrics.TokenDetailsUpdated.Inc()

	logEvent := log.Info().
		Str("address", caAddress).
		Dur("duration", time.Since(start))

	if tokenDetails.TokenName != "" {
		logEvent = logEvent.Str("token_name", tokenDetails.TokenName)
	}
	if tokenDetails.Symbol != "" {
		logEvent = logEvent.Str("symbol", tokenDetails.Symbol)
	}
	if tokenDetails.PriceUSD != nil {
		logEvent = logEvent.Float64("price_usd", *tokenDetails.PriceUSD)
	}
	if tokenDetails.HolderCount != nil {
		logEvent = logEvent.Int64("holder_count", *tokenDetails.HolderCount)
	}

	// Log data sources used
	moralisDataCount := 0
	dexScreenerDataCount := 0
	if metadata != nil {
		moralisDataCount++
	}
	if analytics != nil {
		moralisDataCount++
	}
	if holderCount != nil {
		moralisDataCount++
	}
	if dexPairData != nil {
		dexScreenerDataCount++
	}

	logEvent = logEvent.
		Int("moralis_apis_used", moralisDataCount).
		Int("dexscreener_apis_used", dexScreenerDataCount)

	// Add data quality indicators
	dataQualityScore := 0
	if tokenDetails.TokenName != "" {
		dataQualityScore++
	}
	if tokenDetails.Symbol != "" {
		dataQualityScore++
	}
	if tokenDetails.PriceUSD != nil {
		dataQualityScore++
	}
	if tokenDetails.MarketCapUSD != nil {
		dataQualityScore++
	}
	if tokenDetails.HolderCount != nil {
		dataQualityScore++
	}
	if tokenDetails.TokenLogoURL != "" {
		dataQualityScore++
	}
	if tokenDetails.TokenTwitterURL != "" {
		dataQualityScore++
	}

	logEvent = logEvent.
		Int("data_quality_score", dataQualityScore).
		Int("max_data_fields", 7).
		Bool("has_basic_info", tokenDetails.TokenName != "" && tokenDetails.Symbol != "").
		Bool("has_price_data", tokenDetails.PriceUSD != nil).
		Bool("used_existing_data", existTokenDetail.ID != 0)

	logEvent.Msg("Successfully updated Solana token details with combined data sources")
}

// GetTokenDetails gets all token details for a CA
func (s *TokenService) GetTokenDetails(caAddress string) ([]*db.TokenDetails, error) {
	return s.db.GetTokenDetails(caAddress)
}

// GetTokenDetailsBySource gets token details for a CA from a specific source
func (s *TokenService) GetTokenDetailsBySource(caAddress string, source string) (*db.TokenDetails, error) {
	return s.db.GetTokenDetailsBySource(caAddress, source)
}

// fetchEVMTokenHolderCount fetches token holder count from Moralis EVM API
func (s *TokenService) fetchEVMTokenHolderCount(tokenAddress string, chainID string) (*int64, error) {
	// Wait for rate limiter
	s.moralisRateLimiter.Wait()

	// Map chain ID to Moralis chain format
	moralisChain := chainID
	switch chainID {
	case "ethereum":
		moralisChain = "eth"
	case "bsc":
		moralisChain = "bsc"
	case "polygon":
		moralisChain = "polygon"
	case "base":
		moralisChain = "base"
	}

	// Build the API URL
	apiURL := fmt.Sprintf("https://deep-index.moralis.io/api/v2.2/erc20/%s/holders?chain=%s", tokenAddress, moralisChain)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to Moralis EVM API")
		return nil, err
	}

	// Add API key header
	req.Header.Add("X-API-Key", s.moralisConfig.APIKey)

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching token holder count from Moralis EVM API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_holders", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("moralis", "evm_token_holders").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by Moralis EVM API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_holders", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by Moralis EVM API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Str("chain_id", chainID).
			Msg("Error response from Moralis EVM API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_holders", "error").Inc()
		return nil, fmt.Errorf("error response from Moralis EVM API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_holders", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from Moralis EVM API")
		return nil, err
	}

	var holderResponse MoralisEVMTokenHolderResponse
	if err = json.Unmarshal(body, &holderResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from Moralis EVM API")
		return nil, err
	}

	return &holderResponse.TotalHolders, nil
}

// fetchSolanaTokenHolderCount fetches token holder count from Moralis Solana API
func (s *TokenService) fetchSolanaTokenHolderCount(tokenAddress string) (*int64, error) {
	// Wait for rate limiter
	s.moralisRateLimiter.Wait()

	// Build the API URL
	apiURL := fmt.Sprintf("https://solana-gateway.moralis.io/token/mainnet/holders/%s?network=mainnet", tokenAddress)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to Moralis Solana API")
		return nil, err
	}

	// Add API key header
	req.Header.Add("X-API-Key", s.moralisConfig.APIKey)

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching token holder count from Moralis Solana API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_holders", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("moralis", "solana_token_holders").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by Moralis Solana API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_holders", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by Moralis Solana API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Msg("Error response from Moralis Solana API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_holders", "error").Inc()
		return nil, fmt.Errorf("error response from Moralis Solana API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_holders", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from Moralis Solana API")
		return nil, err
	}

	var holderResponse MoralisSolanaTokenHolderResponse
	if err = json.Unmarshal(body, &holderResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from Moralis Solana API")
		return nil, err
	}

	return &holderResponse.TotalHolders, nil
}

// fetchEVMTokenMetadata fetches token metadata from Moralis EVM API
func (s *TokenService) fetchEVMTokenMetadata(tokenAddress string, chainID string) (*MoralisEVMTokenMetadataResponse, error) {
	// Wait for rate limiter
	s.moralisRateLimiter.Wait()

	// Map chain ID to Moralis chain format
	moralisChain := chainID
	switch chainID {
	case "ethereum":
		moralisChain = "eth"
	case "bsc":
		moralisChain = "bsc"
	case "base":
		moralisChain = "base"
	}

	// Build the API URL
	apiURL := fmt.Sprintf("https://deep-index.moralis.io/api/v2.2/erc20/metadata?chain=%s&addresses=%s", moralisChain, tokenAddress)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to Moralis EVM metadata API")
		return nil, err
	}

	// Add API key header
	req.Header.Add("X-API-Key", s.moralisConfig.APIKey)

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching token metadata from Moralis EVM API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_metadata", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("moralis", "evm_token_metadata").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by Moralis EVM metadata API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_metadata", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by Moralis EVM metadata API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Str("chain_id", chainID).
			Msg("Error response from Moralis EVM metadata API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_metadata", "error").Inc()
		return nil, fmt.Errorf("error response from Moralis EVM metadata API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_metadata", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from Moralis EVM metadata API")
		return nil, err
	}

	var metadataResponse []MoralisEVMTokenMetadataResponse
	if err = json.Unmarshal(body, &metadataResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from Moralis EVM metadata API")
		return nil, err
	}

	if len(metadataResponse) == 0 {
		return nil, fmt.Errorf("no metadata found for token %s", tokenAddress)
	}

	return &metadataResponse[0], nil
}

// fetchEVMTokenPrice fetches token price from Moralis EVM API
func (s *TokenService) fetchEVMTokenPrice(tokenAddress string, chainID string) (*MoralisEVMTokenPriceResponse, error) {
	// Wait for rate limiter
	s.moralisRateLimiter.Wait()

	// Map chain ID to Moralis chain format
	moralisChain := chainID
	switch chainID {
	case "ethereum":
		moralisChain = "eth"
	case "bsc":
		moralisChain = "bsc"
	case "base":
		moralisChain = "base"
	}

	// Build the API URL
	apiURL := fmt.Sprintf("https://deep-index.moralis.io/api/v2.2/erc20/%s/price?chain=%s&include=percent_change", tokenAddress, moralisChain)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to Moralis EVM price API")
		return nil, err
	}

	// Add API key header
	req.Header.Add("X-API-Key", s.moralisConfig.APIKey)

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching token price from Moralis EVM API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_price", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("moralis", "evm_token_price").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by Moralis EVM price API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_price", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by Moralis EVM price API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Str("chain_id", chainID).
			Msg("Error response from Moralis EVM price API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_price", "error").Inc()
		return nil, fmt.Errorf("error response from Moralis EVM price API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "evm_token_price", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from Moralis EVM price API")
		return nil, err
	}

	var priceResponse MoralisEVMTokenPriceResponse
	if err = json.Unmarshal(body, &priceResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from Moralis EVM price API")
		return nil, err
	}

	return &priceResponse, nil
}

// fetchSolanaTokenMetadata fetches token metadata from Moralis Solana API
func (s *TokenService) fetchSolanaTokenMetadata(tokenAddress string) (*MoralisSolanaTokenMetadataResponse, error) {
	// Wait for rate limiter
	s.moralisRateLimiter.Wait()

	// Build the API URL
	apiURL := fmt.Sprintf("https://solana-gateway.moralis.io/token/mainnet/%s/metadata", tokenAddress)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to Moralis Solana metadata API")
		return nil, err
	}

	// Add API key header
	req.Header.Add("X-API-Key", s.moralisConfig.APIKey)

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching token metadata from Moralis Solana API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_metadata", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("moralis", "solana_token_metadata").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by Moralis Solana metadata API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_metadata", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by Moralis Solana metadata API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Msg("Error response from Moralis Solana metadata API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_metadata", "error").Inc()
		return nil, fmt.Errorf("error response from Moralis Solana metadata API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_metadata", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from Moralis Solana metadata API")
		return nil, err
	}

	var metadataResponse MoralisSolanaTokenMetadataResponse
	if err = json.Unmarshal(body, &metadataResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from Moralis Solana metadata API")
		return nil, err
	}

	return &metadataResponse, nil
}

// fetchSolanaTokenPrice fetches token price from Moralis Solana API
// Used as a fallback when price cannot be obtained from analytics or DexScreener
func (s *TokenService) fetchSolanaTokenPrice(tokenAddress string) (*MoralisSolanaTokenPriceResponse, error) {
	// Wait for rate limiter
	s.moralisRateLimiter.Wait()

	// Build the API URL GET https://solana-gateway.moralis.io/token/:network/:address/price
	apiURL := fmt.Sprintf("https://solana-gateway.moralis.io/token/mainnet/%s/price", tokenAddress)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to Moralis Solana price API")
		return nil, err
	}

	// Add API key header
	req.Header.Add("X-API-Key", s.moralisConfig.APIKey)

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching token price from Moralis Solana API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_price", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("moralis", "solana_token_price").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by Moralis Solana price API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_price", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by Moralis Solana price API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Msg("Error response from Moralis Solana price API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_price", "error").Inc()
		return nil, fmt.Errorf("error response from Moralis Solana price API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_price", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from Moralis Solana price API")
		return nil, err
	}

	var priceResponse MoralisSolanaTokenPriceResponse
	if err = json.Unmarshal(body, &priceResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from Moralis Solana price API")
		return nil, err
	}

	return &priceResponse, nil
}

// fetchSolanaTokenAnalytics fetches token analytics from Moralis Solana API
func (s *TokenService) fetchSolanaTokenAnalytics(tokenAddress string) (*MoralisSolanaTokenAnalyticsResponse, error) {
	// Wait for rate limiter
	s.moralisRateLimiter.Wait()

	// Build the API URL https://deep-index.moralis.io/api/v2.2/tokens/:address/analytics
	apiURL := fmt.Sprintf("https://deep-index.moralis.io/api/v2.2/tokens/%s/analytics", tokenAddress)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to Moralis Solana analytics API")
		return nil, err
	}

	// Add API key header
	req.Header.Add("X-API-Key", s.moralisConfig.APIKey)

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching token analytics from Moralis Solana API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_analytics", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("moralis", "solana_token_analytics").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by Moralis Solana analytics API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_analytics", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by Moralis Solana analytics API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Msg("Error response from Moralis Solana analytics API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_analytics", "error").Inc()
		return nil, fmt.Errorf("error response from Moralis Solana analytics API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("moralis", "solana_token_analytics", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from Moralis Solana analytics API")
		return nil, err
	}

	var analyticsResponse MoralisSolanaTokenAnalyticsResponse
	if err = json.Unmarshal(body, &analyticsResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from Moralis Solana analytics API")
		return nil, err
	}

	return &analyticsResponse, nil
}

// fetchDexScreenerPairData fetches pair data from DexScreener API (for Solana token creation time only)
func (s *TokenService) fetchDexScreenerPairData(tokenAddress string, chainID string) (*DexScreenerResponse, error) {
	// Wait for rate limiter
	s.rateLimiter.Wait()

	// Build the API URL
	apiURL := fmt.Sprintf("%s/tokens/v1/%s/%s", s.config.BaseURL, chainID, tokenAddress)

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error creating request to DexScreener API")
		return nil, err
	}

	// Record API request metrics
	apiStart := time.Now()

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Debug().
			Err(err).
			Str("url", apiURL).
			Msg("Error fetching pair data from DexScreener API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("dexscreener", "tokens", "error").Inc()
		return nil, err
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("dexscreener", "tokens").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		log.Debug().
			Msg("Rate limited by DexScreener API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("dexscreener", "tokens", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by DexScreener API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Debug().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Str("address", tokenAddress).
			Str("chain_id", chainID).
			Msg("Error response from DexScreener API")
		metrics.ExternalAPIRequestsTotal.WithLabelValues("dexscreener", "tokens", "error").Inc()
		return nil, fmt.Errorf("error response from DexScreener API: %d", resp.StatusCode)
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("dexscreener", "tokens", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Debug().
			Err(err).
			Msg("Error reading response body from DexScreener API")
		return nil, err
	}

	var dexResponse []DexScreenerResponse
	if err = json.Unmarshal(body, &dexResponse); err != nil {
		log.Debug().
			Err(err).
			Str("address", tokenAddress).
			Msg("Error parsing response from DexScreener API")
		return nil, err
	}

	if len(dexResponse) == 0 {
		return nil, fmt.Errorf("no pair data found for token %s", tokenAddress)
	}

	return &dexResponse[0], nil
}
