package services

import (
	"sync"
	"time"
)

// RateLimiter implements a token bucket rate limiter
type RateLimiter struct {
	rate       int           // Number of tokens per interval
	interval   time.Duration // Interval for token replenishment
	tokens     int           // Current number of tokens
	lastReplenish time.Time  // Last time tokens were replenished
	mu         sync.Mutex    // Mutex for thread safety
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(rate int, interval time.Duration) *RateLimiter {
	return &RateLimiter{
		rate:     rate,
		interval: interval,
		tokens:   rate,
		lastReplenish: time.Now(),
	}
}

// Wait blocks until a token is available
func (r *RateLimiter) Wait() {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// Replenish tokens if needed
	r.replenishTokens()
	
	// If no tokens available, wait until next replenishment
	for r.tokens <= 0 {
		r.mu.Unlock()
		time.Sleep(10 * time.Millisecond)
		r.mu.Lock()
		r.replenishTokens()
	}
	
	// Take a token
	r.tokens--
}

// replenishTokens replenishes tokens based on elapsed time
func (r *RateLimiter) replenishTokens() {
	now := time.Now()
	elapsed := now.Sub(r.lastReplenish)
	
	// Calculate how many tokens to add
	tokensToAdd := int(float64(r.rate) * elapsed.Seconds() / r.interval.Seconds())
	if tokensToAdd > 0 {
		r.tokens = min(r.rate, r.tokens+tokensToAdd)
		r.lastReplenish = now
	}
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
