package services

import (
	"context"
	"strconv"
	"testing"
	"time"

	"real-time-ca-service/internal/config"

	"github.com/alicebob/miniredis/v2"
	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestRedisWithPrefix(t *testing.T, prefix string) (*RedisService, func()) {
	// Start a mini Redis server for testing
	mr, err := miniredis.Run()
	require.NoError(t, err)

	port, err := strconv.Atoi(mr.Port())
	require.NoError(t, err)

	cfg := config.RedisConfig{
		Host:                mr.Host(),
		Port:                port,
		LockTimeout:         30 * time.Second,
		LockRetryDelay:      100 * time.Millisecond,
		LockMaxRetries:      10,
		LockRefreshInterval: 10 * time.Second,
		LockCleanupTimeout:  5 * time.Second,
		KeyPrefix:           prefix,
		CleanupOldKeys:      true,
		OldKeyPatterns:      []string{"tweet_lock:*", "old_*"},
	}

	// Create Redis client with miniredis address
	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
		DB:   0,
	})

	// Create Redis service manually for testing
	redisService := &RedisService{
		client: client,
		config: cfg,
	}

	// Initialize the locker
	redisService.locker = redislock.New(client)

	cleanup := func() {
		client.Close()
		mr.Close()
	}

	return redisService, cleanup
}

func TestDistributedLockManager_KeyPrefix(t *testing.T) {
	redisService, cleanup := setupTestRedisWithPrefix(t, "v2_")
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	ctx := context.Background()

	t.Run("lock keys use prefix", func(t *testing.T) {
		// Test CreateTweetLockKey method
		key := lockManager.CreateTweetLockKey("123")
		assert.Equal(t, "v2_tweet_lock:123", key)

		// Test actual lock acquisition
		lock, err := lockManager.AcquireLock(ctx, key, nil)
		require.NoError(t, err)
		require.NotNil(t, lock)

		// Verify the lock key in Redis
		client := redisService.GetClient()
		exists := client.Exists(ctx, "v2_tweet_lock:123").Val()
		assert.Equal(t, int64(1), exists)

		// Verify old format doesn't exist
		oldExists := client.Exists(ctx, "tweet_lock:123").Val()
		assert.Equal(t, int64(0), oldExists)

		// Clean up
		lock.ReleaseLock()
	})

	t.Run("different prefixes don't conflict", func(t *testing.T) {
		// Create another lock manager with different prefix
		redisService2, cleanup2 := setupTestRedisWithPrefix(t, "v3_")
		defer cleanup2()

		lockManager2 := NewDistributedLockManager(redisService2)

		// Both managers can acquire "locks" for the same tweet ID
		lock1, err := lockManager.AcquireLock(ctx, lockManager.CreateTweetLockKey("456"), nil)
		require.NoError(t, err)

		lock2, err := lockManager2.AcquireLock(ctx, lockManager2.CreateTweetLockKey("456"), nil)
		require.NoError(t, err)

		// Verify both locks exist with different keys
		client := redisService.GetClient()
		v2Exists := client.Exists(ctx, "v2_tweet_lock:456").Val()
		assert.Equal(t, int64(1), v2Exists)

		client2 := redisService2.GetClient()
		v3Exists := client2.Exists(ctx, "v3_tweet_lock:456").Val()
		assert.Equal(t, int64(1), v3Exists)

		// Clean up
		lock1.ReleaseLock()
		lock2.ReleaseLock()
	})
}

func TestCreateTweetLockKeyFunctions(t *testing.T) {
	t.Run("CreateTweetLockKey without prefix", func(t *testing.T) {
		key := CreateTweetLockKey("123")
		assert.Equal(t, "tweet_lock:123", key)
	})

	t.Run("CreateTweetLockKeyWithPrefix", func(t *testing.T) {
		key := CreateTweetLockKeyWithPrefix("v2_", "123")
		assert.Equal(t, "v2_tweet_lock:123", key)

		key2 := CreateTweetLockKeyWithPrefix("test_", "456")
		assert.Equal(t, "test_tweet_lock:456", key2)

		key3 := CreateTweetLockKeyWithPrefix("", "789")
		assert.Equal(t, "tweet_lock:789", key3)
	})
}

func TestRedisService_CheckRedisEnvironment(t *testing.T) {
	redisService, cleanup := setupTestRedisWithPrefix(t, "v2_")
	defer cleanup()

	ctx := context.Background()
	client := redisService.GetClient()

	t.Run("no old keys", func(t *testing.T) {
		err := redisService.CheckRedisEnvironment(ctx)
		assert.NoError(t, err)
	})

	t.Run("old keys found and cleaned up", func(t *testing.T) {
		// Add some old keys
		client.Set(ctx, "tweet_lock:123", "old_value", 0)
		client.Set(ctx, "tweet_lock:456", "old_value", 0)
		client.Set(ctx, "old_cache:789", "old_value", 0)

		// Verify old keys exist
		assert.Equal(t, int64(1), client.Exists(ctx, "tweet_lock:123").Val())
		assert.Equal(t, int64(1), client.Exists(ctx, "tweet_lock:456").Val())
		assert.Equal(t, int64(1), client.Exists(ctx, "old_cache:789").Val())

		// Run environment check
		err := redisService.CheckRedisEnvironment(ctx)
		assert.NoError(t, err)

		// Verify old keys are cleaned up
		assert.Equal(t, int64(0), client.Exists(ctx, "tweet_lock:123").Val())
		assert.Equal(t, int64(0), client.Exists(ctx, "tweet_lock:456").Val())
		assert.Equal(t, int64(0), client.Exists(ctx, "old_cache:789").Val())
	})

	t.Run("cleanup disabled", func(t *testing.T) {
		// Create service with cleanup disabled
		redisServiceNoCleanup, cleanupNoCleanup := setupTestRedisWithPrefix(t, "v2_")
		defer cleanupNoCleanup()
		
		redisServiceNoCleanup.config.CleanupOldKeys = false
		clientNoCleanup := redisServiceNoCleanup.GetClient()

		// Add old keys
		clientNoCleanup.Set(ctx, "tweet_lock:999", "old_value", 0)

		// Verify old key exists
		assert.Equal(t, int64(1), clientNoCleanup.Exists(ctx, "tweet_lock:999").Val())

		// Run environment check
		err := redisServiceNoCleanup.CheckRedisEnvironment(ctx)
		assert.NoError(t, err)

		// Verify old key still exists (not cleaned up)
		assert.Equal(t, int64(1), clientNoCleanup.Exists(ctx, "tweet_lock:999").Val())
	})

	t.Run("no patterns configured", func(t *testing.T) {
		// Create service with no patterns
		redisServiceNoPatterns, cleanupNoPatterns := setupTestRedisWithPrefix(t, "v2_")
		defer cleanupNoPatterns()
		
		redisServiceNoPatterns.config.OldKeyPatterns = nil

		// Run environment check
		err := redisServiceNoPatterns.CheckRedisEnvironment(ctx)
		assert.NoError(t, err)
	})
}

func TestRedisService_CleanupKeys(t *testing.T) {
	redisService, cleanup := setupTestRedisWithPrefix(t, "v2_")
	defer cleanup()

	ctx := context.Background()
	client := redisService.GetClient()

	t.Run("cleanup empty key list", func(t *testing.T) {
		err := redisService.cleanupKeys(ctx, []string{}, "test_pattern")
		assert.NoError(t, err)
	})

	t.Run("cleanup existing keys", func(t *testing.T) {
		// Add test keys
		keys := []string{"test_key_1", "test_key_2", "test_key_3"}
		for _, key := range keys {
			client.Set(ctx, key, "test_value", 0)
		}

		// Verify keys exist
		for _, key := range keys {
			assert.Equal(t, int64(1), client.Exists(ctx, key).Val())
		}

		// Cleanup keys
		err := redisService.cleanupKeys(ctx, keys, "test_*")
		assert.NoError(t, err)

		// Verify keys are deleted
		for _, key := range keys {
			assert.Equal(t, int64(0), client.Exists(ctx, key).Val())
		}
	})
}

func TestGetSampleKeys(t *testing.T) {
	t.Run("fewer keys than sample size", func(t *testing.T) {
		keys := []string{"key1", "key2", "key3"}
		sample := getSampleKeys(keys, 5)
		assert.Equal(t, keys, sample)
	})

	t.Run("more keys than sample size", func(t *testing.T) {
		keys := []string{"key1", "key2", "key3", "key4", "key5", "key6"}
		sample := getSampleKeys(keys, 3)
		expected := []string{"key1", "key2", "key3"}
		assert.Equal(t, expected, sample)
	})

	t.Run("empty keys", func(t *testing.T) {
		keys := []string{}
		sample := getSampleKeys(keys, 5)
		assert.Equal(t, keys, sample)
	})
}
