package services

import (
	"testing"

	"real-time-ca-service/internal/config"

	"github.com/stretchr/testify/assert"
)

func TestCreateTweetLockKey(t *testing.T) {
	tests := []struct {
		name     string
		tweetID  string
		expected string
	}{
		{
			name:     "normal tweet ID",
			tweetID:  "1234567890",
			expected: "tweet_lock:1234567890",
		},
		{
			name:     "tweet ID with special characters",
			tweetID:  "tweet_123-abc",
			expected: "tweet_lock:tweet_123-abc",
		},
		{
			name:     "empty tweet ID",
			tweetID:  "",
			expected: "tweet_lock:",
		},
		{
			name:     "long tweet ID",
			tweetID:  "1234567890123456789012345678901234567890",
			expected: "tweet_lock:1234567890123456789012345678901234567890",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CreateTweetLockKey(tt.tweetID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNewDistributedLockManager(t *testing.T) {
	// Create a mock Redis service
	cfg := config.RedisConfig{
		LockTimeout:         30000000000, // 30 seconds in nanoseconds
		LockRetryDelay:      100000000,   // 100ms in nanoseconds
		LockMaxRetries:      10,
		LockRefreshInterval: 10000000000, // 10 seconds in nanoseconds
	}

	redisService := &RedisService{
		config: cfg,
	}

	lockManager := NewDistributedLockManager(redisService)

	assert.NotNil(t, lockManager)
	assert.Equal(t, cfg.LockTimeout, lockManager.defaultTimeout)
	assert.Equal(t, cfg.LockRetryDelay, lockManager.defaultRetryDelay)
	assert.Equal(t, cfg.LockMaxRetries, lockManager.defaultMaxRetries)
	assert.Equal(t, cfg.LockRefreshInterval, lockManager.refreshInterval)
}

func TestTwitterService_WithRedisService(t *testing.T) {
	// Test that TwitterService can be created with a Redis service
	cfg := config.SocialDataConfig{
		RequestsPerSec: 10,
	}

	redisConfig := config.RedisConfig{
		Host:                "localhost",
		Port:                6379,
		LockTimeout:         30000000000, // 30 seconds
		LockRetryDelay:      100000000,   // 100ms
		LockMaxRetries:      10,
		LockRefreshInterval: 10000000000, // 10 seconds
	}

	redisService := &RedisService{
		config: redisConfig,
	}

	lockManager := NewDistributedLockManager(redisService)
	duplicateDetectorConfig := config.DuplicateDetectorConfig{
		Enabled:             true,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}
	twitterService := NewTwitterService(cfg, nil, nil, nil, redisService, lockManager, duplicateDetectorConfig)

	assert.NotNil(t, twitterService)
	assert.NotNil(t, twitterService.redisService)
	assert.NotNil(t, twitterService.lockManager)
	assert.Equal(t, redisService, twitterService.redisService)
	assert.Equal(t, lockManager, twitterService.lockManager)
}

func TestTwitterService_WithoutRedisService(t *testing.T) {
	// Test that TwitterService can be created without a Redis service
	cfg := config.SocialDataConfig{
		RequestsPerSec: 10,
	}

	duplicateDetectorConfig := config.DuplicateDetectorConfig{
		Enabled:             false,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}
	twitterService := NewTwitterService(cfg, nil, nil, nil, nil, nil, duplicateDetectorConfig)

	assert.NotNil(t, twitterService)
	assert.Nil(t, twitterService.redisService)
	assert.Nil(t, twitterService.lockManager)
}

func TestLockOptions_DefaultValues(t *testing.T) {
	// Test that LockOptions can be created with default values
	opts := &LockOptions{}

	assert.Equal(t, 0, opts.MaxRetries)
	assert.Equal(t, "", opts.Metadata)
}

func TestLockOptions_CustomValues(t *testing.T) {
	// Test that LockOptions can be created with custom values
	opts := &LockOptions{
		Timeout:    30000000000, // 30 seconds
		RetryDelay: 100000000,   // 100ms
		MaxRetries: 5,
		Metadata:   "test_metadata",
	}

	assert.Equal(t, int64(30000000000), int64(opts.Timeout))
	assert.Equal(t, int64(100000000), int64(opts.RetryDelay))
	assert.Equal(t, 5, opts.MaxRetries)
	assert.Equal(t, "test_metadata", opts.Metadata)
}

// TestRedisConfig_DefaultValues tests that Redis configuration has sensible defaults
func TestRedisConfig_DefaultValues(t *testing.T) {
	cfg := config.RedisConfig{}

	// Test that zero values are handled properly
	assert.Equal(t, "", cfg.Host)
	assert.Equal(t, 0, cfg.Port)
	assert.Equal(t, 0, cfg.DB)
}

// TestRedisConfig_ProductionValues tests production-ready configuration values
func TestRedisConfig_ProductionValues(t *testing.T) {
	cfg := config.RedisConfig{
		Host:                "redis.example.com",
		Port:                6379,
		Password:            "secure_password",
		DB:                  0,
		PoolSize:            20,
		MinIdleConns:        5,
		MaxRetries:          3,
		LockTimeout:         30000000000, // 30 seconds
		LockRetryDelay:      100000000,   // 100ms
		LockMaxRetries:      10,
		LockRefreshInterval: 10000000000, // 10 seconds
	}

	assert.Equal(t, "redis.example.com", cfg.Host)
	assert.Equal(t, 6379, cfg.Port)
	assert.Equal(t, "secure_password", cfg.Password)
	assert.Equal(t, 0, cfg.DB)
	assert.Equal(t, 20, cfg.PoolSize)
	assert.Equal(t, 5, cfg.MinIdleConns)
	assert.Equal(t, 3, cfg.MaxRetries)
	assert.Equal(t, int64(30000000000), int64(cfg.LockTimeout))
	assert.Equal(t, int64(100000000), int64(cfg.LockRetryDelay))
	assert.Equal(t, 10, cfg.LockMaxRetries)
	assert.Equal(t, int64(10000000000), int64(cfg.LockRefreshInterval))
}
