package services

import (
	"testing"
)

func TestExtractAddresses(t *testing.T) {
	// Create a new CAService
	service := NewCAService(nil, nil, nil)

	// Test cases
	testCases := []struct {
		name     string
		text     string
		expected []string
	}{
		{
			name:     "No addresses",
			text:     "This text contains no contract addresses",
			expected: []string{},
		},
		{
			name:     "Single evm address",
			text:     "Check out this new token: 0x1234567890abcdef1234567890abcdef12345678",
			expected: []string{"0x1234567890abcdef1234567890abcdef12345678"},
		},
		{
			name:     "Multiple evm addresses",
			text:     "Compare these tokens: 0x1234567890abcdef1234567890abcdef12345678 and 0xabcdef1234567890abcdef1234567890abcdef12",
			expected: []string{"0x1234567890abcdef1234567890abcdef12345678", "0xabcdef1234567890abcdef1234567890abcdef12"},
		},
		{
			name:     "solana address",
			text:     "solana token: 9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b",
			expected: []string{"9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b"},
		},
		{
			name:     "Mixed addresses",
			text:     "evm: 0x1234567890abcdef1234567890abcdef12345678 solana: 9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b",
			expected: []string{"0x1234567890abcdef1234567890abcdef12345678", "9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b"},
		},
		{
			name:     "Duplicate addresses",
			text:     "Same address twice: 0x1234567890abcdef1234567890abcdef12345678 0x1234567890abcdef1234567890abcdef12345678",
			expected: []string{"0x1234567890abcdef1234567890abcdef12345678"},
		},
		{
			name:     "Address with Ai agent CA keyword",
			text:     "Ai agent CA: 0x1234567890abcdef1234567890abcdef12345678",
			expected: []string{"0x1234567890abcdef1234567890abcdef12345678"},
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := service.extractAddresses(tc.text)

			// Check if the result has the expected length
			if len(result) != len(tc.expected) {
				t.Errorf("Expected %d addresses, got %d", len(tc.expected), len(result))
				return
			}

			// Create a map of expected addresses for easier comparison
			expectedMap := make(map[string]bool)
			for _, addr := range tc.expected {
				expectedMap[addr] = true
			}

			// Check if all extracted addresses are in the expected set
			for _, addr := range result {
				if !expectedMap[addr] {
					t.Errorf("Unexpected address extracted: %s", addr)
				}
			}
		})
	}
}

func TestDetectChainType(t *testing.T) {
	// Create a new CAService
	service := NewCAService(nil, nil, nil)

	// Test cases
	testCases := []struct {
		name     string
		address  string
		expected string
	}{
		{
			name:     "Valid evm address",
			address:  "0x1234567890abcdef1234567890abcdef12345678",
			expected: "evm",
		},
		{
			name:     "Invalid evm address (too short)",
			address:  "0x1234567890abcdef",
			expected: "",
		},
		{
			name:     "Invalid evm address (no 0x prefix)",
			address:  "1234567890abcdef1234567890abcdef12345678",
			expected: "",
		},
		{
			name:     "Valid solana address",
			address:  "9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b",
			expected: "solana",
		},
		{
			name:     "Invalid solana address (too short)",
			address:  "9ZNTfG4NyQg",
			expected: "",
		},
		{
			name:     "Invalid address (empty)",
			address:  "",
			expected: "",
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := service.detectChainType(tc.address)
			if result != tc.expected {
				t.Errorf("Expected chain type %q, got %q", tc.expected, result)
			}
		})
	}
}
