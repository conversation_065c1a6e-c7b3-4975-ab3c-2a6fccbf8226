package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/config"
)

const (
	telegramAPIBaseURL = "https://api.telegram.org/bot%s/%s"
	sendMessageMethod  = "sendMessage"
	maxRetries         = 3
	retryDelay         = 5 * time.Second
)

// TelegramService handles sending messages via Telegram Bot API
type TelegramService struct {
	config     *config.TelegramConfig
	httpClient *http.Client
}

// NewTelegramService creates a new TelegramService
func NewTelegramService(cfg *config.TelegramConfig) *TelegramService {
	if cfg == nil {
		return &TelegramService{config: &config.TelegramConfig{Enabled: false}} // Return a disabled service if config is nil
	}
	return &TelegramService{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SendMessage sends a message to the configured Telegram chat.
// It will be expanded to handle different tweet types and formatting.
func (s *TelegramService) SendMessage(text string) error {
	if !s.config.Enabled {
		log.Debug().Msg("Telegram service is disabled. Skipping message send.")
		return nil
	}

	if s.config.BotToken == "" || s.config.ChatID == "" {
		log.Error().Msg("Telegram BotToken or ChatID is not configured.")
		return fmt.Errorf("telegram bot_token or chat_id is not configured")
	}

	apiURL := fmt.Sprintf(telegramAPIBaseURL, s.config.BotToken, sendMessageMethod)

	payload := map[string]interface{}{
		"chat_id":    s.config.ChatID,
		"text":       text,
		"parse_mode": "Markdown", // Using MarkdownV2 for better formatting
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		log.Error().Err(err).Interface("payload", payload).Msg("Failed to marshal Telegram message payload")
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	var lastErr error
	for i := 0; i < maxRetries; i++ {
		req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonPayload))
		if err != nil {
			log.Error().Err(err).Interface("payload", payload).Msg("Failed to create Telegram request")
			return fmt.Errorf("failed to create request: %w", err) // Non-retryable error
		}
		req.Header.Set("Content-Type", "application/json")

		resp, err := s.httpClient.Do(req)
		if err != nil {
			log.Warn().Err(err).Int("attempt", i+1).Interface("payload", payload).Msg("Failed to send Telegram message")
			lastErr = fmt.Errorf("failed to send request: %w", err)
			time.Sleep(retryDelay)
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			log.Info().Str("chat_id", s.config.ChatID).Msg("Telegram message sent successfully")
			return nil
		}

		// Handle non-OK status codes
		var bodyBytes []byte
		bodyBytes, _ = io.ReadAll(resp.Body)
		log.Warn().Int("status_code", resp.StatusCode).Str("response_body", string(bodyBytes)).Int("attempt", i+1).Interface("payload", payload).Msg("Failed to send Telegram message")
		lastErr = fmt.Errorf("telegram API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))

		if resp.StatusCode == http.StatusBadRequest || resp.StatusCode == http.StatusUnauthorized || resp.StatusCode == http.StatusForbidden {
			// Non-retryable errors for these status codes
			log.Error().Int("status_code", resp.StatusCode).Str("response_body", string(bodyBytes)).Interface("payload", payload).Msg("Non-retryable error from Telegram API")
			return lastErr
		}
		time.Sleep(retryDelay)
	}

	log.Error().Err(lastErr).Int("retries", maxRetries).Interface("payload", payload).Msg("Failed to send Telegram message after multiple retries")
	return fmt.Errorf("failed to send telegram message after %d retries: %w", maxRetries, lastErr)
}

// Helper function to escape MarkdownV2 characters
// Telegram API requires specific characters to be escaped in MarkdownV2 mode.
// Characters: _, *, [, ], (, ), ~, `, >, #, +, -, =, |, {, }, ., !
func escapeMarkdownV2(text string) string {
	escapeChars := []string{"_", "*", "[", "]", "(", ")", "~", "`", ">", "#", "+", "-", "=", "|", "{", "}", ".", "!"}
	var result strings.Builder
	for _, r := range text {
		s := string(r)
		found := false
		for _, char := range escapeChars {
			if s == char {
				result.WriteString("\\")
				result.WriteString(s)
				found = true
				break
			}
		}
		if !found {
			result.WriteString(s)
		}
	}
	return result.String()
}
