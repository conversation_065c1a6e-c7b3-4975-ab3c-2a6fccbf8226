package services

import (
	"testing"

	"real-time-ca-service/internal/config"

	"github.com/stretchr/testify/assert"
)

func TestDuplicateDetector_PreprocessText(t *testing.T) {
	cfg := config.DuplicateDetectorConfig{
		Enabled:             true,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}
	detector := NewDuplicateDetector(cfg, nil)

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Basic text",
			input:    "Hello World",
			expected: "hello world",
		},
		{
			name:     "Text with URLs",
			input:    "Check this out https://example.com/path",
			expected: "check this out",
		},
		{
			name:     "Text with mentions",
			input:    "Hey @username how are you?",
			expected: "hey how are you",
		},
		{
			name:     "Text with hashtags",
			input:    "This is #awesome #crypto news",
			expected: "this is awesome crypto news",
		},
		{
			name:     "Text with punctuation",
			input:    "Hello, world! How are you?",
			expected: "hello world how are you",
		},
		{
			name:     "Complex text",
			input:    "🚀 New #AI agent CA: 0x1234...abcd @everyone check https://dexscreener.com/ethereum/0x1234",
			expected: "new ai agent ca 0x1234abcd check",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detector.preprocessText(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDuplicateDetector_CalculateJaccardSimilarity(t *testing.T) {
	cfg := config.DuplicateDetectorConfig{
		Enabled:             true,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}
	detector := NewDuplicateDetector(cfg, nil)

	tests := []struct {
		name     string
		text1    string
		text2    string
		expected float64
	}{
		{
			name:     "Identical texts",
			text1:    "hello world",
			text2:    "hello world",
			expected: 1.0,
		},
		{
			name:     "Completely different texts",
			text1:    "hello world",
			text2:    "foo bar",
			expected: 0.0,
		},
		{
			name:     "Partial overlap",
			text1:    "hello world test",
			text2:    "hello world example",
			expected: 0.5, // 2 common words out of 4 total unique words
		},
		{
			name:     "Empty texts",
			text1:    "",
			text2:    "",
			expected: 1.0,
		},
		{
			name:     "One empty text",
			text1:    "hello world",
			text2:    "",
			expected: 0.0,
		},
		{
			name:     "High similarity",
			text1:    "new ai agent token launched",
			text2:    "new ai agent token released",
			expected: 0.6, // 3 common words out of 5 total unique words
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detector.calculateJaccardSimilarity(tt.text1, tt.text2)
			assert.InDelta(t, tt.expected, result, 0.01, "Similarity should be within 0.01 of expected")
		})
	}
}

func TestDuplicateDetector_GetWordSet(t *testing.T) {
	cfg := config.DuplicateDetectorConfig{
		Enabled:             true,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}
	detector := NewDuplicateDetector(cfg, nil)

	tests := []struct {
		name     string
		input    string
		expected map[string]bool
	}{
		{
			name:  "Basic text",
			input: "hello world test",
			expected: map[string]bool{
				"hello": true,
				"world": true,
				"test":  true,
			},
		},
		{
			name:  "Text with short words",
			input: "a big test is ok",
			expected: map[string]bool{
				"big":  true,
				"test": true,
			},
		},
		{
			name:     "Empty text",
			input:    "",
			expected: map[string]bool{},
		},
		{
			name:  "Duplicate words",
			input: "test test hello world hello",
			expected: map[string]bool{
				"test":  true,
				"hello": true,
				"world": true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detector.getWordSet(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDuplicateDetector_CalculateCosineSimilarity(t *testing.T) {
	cfg := config.DuplicateDetectorConfig{
		Enabled:             true,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}
	detector := NewDuplicateDetector(cfg, nil)

	tests := []struct {
		name     string
		text1    string
		text2    string
		expected float64
	}{
		{
			name:     "Identical texts",
			text1:    "hello world",
			text2:    "hello world",
			expected: 1.0,
		},
		{
			name:     "Completely different texts",
			text1:    "hello world",
			text2:    "foo bar",
			expected: 0.0,
		},
		{
			name:     "Empty texts",
			text1:    "",
			text2:    "",
			expected: 1.0,
		},
		{
			name:     "One empty text",
			text1:    "hello world",
			text2:    "",
			expected: 0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detector.calculateCosineSimilarity(tt.text1, tt.text2)
			assert.InDelta(t, tt.expected, result, 0.01, "Cosine similarity should be within 0.01 of expected")
		})
	}
}

func TestDuplicateDetector_ConfigDisabled(t *testing.T) {
	cfg := config.DuplicateDetectorConfig{
		Enabled:             false,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}
	detector := NewDuplicateDetector(cfg, nil)

	// When disabled, DetectAndMarkDuplicates should return nil without doing anything
	err := detector.DetectAndMarkDuplicates("test tweet", "123", "user123")
	assert.NoError(t, err)
}

func TestNewDuplicateDetector(t *testing.T) {
	cfg := config.DuplicateDetectorConfig{
		Enabled:             true,
		SimilarityThreshold: 0.8,
		TimeWindowDays:      15,
		MaxTweetsToCompare:  50,
	}

	detector := NewDuplicateDetector(cfg, nil)

	assert.NotNil(t, detector)
	assert.Equal(t, cfg.Enabled, detector.config.Enabled)
	assert.Equal(t, cfg.SimilarityThreshold, detector.config.SimilarityThreshold)
	assert.Equal(t, cfg.TimeWindowDays, detector.config.TimeWindowDays)
	assert.Equal(t, cfg.MaxTweetsToCompare, detector.config.MaxTweetsToCompare)
}
