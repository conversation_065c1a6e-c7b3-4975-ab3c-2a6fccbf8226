package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/ai"
	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/db"
	"real-time-ca-service/internal/metrics"
	"real-time-ca-service/internal/utils"
)

// TwitterService handles interactions with the Twitter API
type TwitterService struct {
	Config            config.SocialDataConfig
	db                *db.Database
	caService         *CAService
	aiService         *ai.Service
	telegramService   *TelegramService        // Added for Telegram notifications
	redisService      *RedisService           // Added for distributed locking
	lockManager       *DistributedLockManager // Distributed lock manager
	duplicateDetector *DuplicateDetector      // Added for duplicate content detection
	httpClient        *http.Client

	// For controlling the monitoring goroutine
	stopChan chan struct{}
	wg       sync.WaitGroup

	// For rate limiting
	rateLimiter *RateLimiter

	// For periodic tweet updates
	periodicUpdateStopChan chan struct{}
	periodicUpdateWg       sync.WaitGroup
}

// TwitterSearchResponse represents the response from the Twitter search API
type TwitterSearchResponse struct {
	Tweets []Tweet `json:"tweets"`
	Cursor string  `json:"next_cursor"`
}

// Tweet represents a tweet from the Twitter API
type Tweet struct {
	IdStr                string        `json:"id_str"`
	User                 User          `json:"user"`
	Entities             Entities      `json:"entities"`
	TweetCreatedAt       time.Time     `json:"tweet_created_at"`
	Id                   int64         `json:"id"`
	Type                 string        `json:"type"`
	ConversationIdStr    string        `json:"conversation_id_str"`
	CommunityIdStr       interface{}   `json:"community_id_str"`
	CommunityName        interface{}   `json:"community_name"`
	Text                 interface{}   `json:"text"`
	FullText             string        `json:"full_text"`
	Source               string        `json:"source"`
	Truncated            bool          `json:"truncated"`
	InReplyToStatusId    *int64        `json:"in_reply_to_status_id"`
	InReplyToStatusIdStr *string       `json:"in_reply_to_status_id_str"`
	InReplyToUserId      *int64        `json:"in_reply_to_user_id"`
	InReplyToUserIdStr   *string       `json:"in_reply_to_user_id_str"`
	InReplyToScreenName  *string       `json:"in_reply_to_screen_name"`
	QuotedStatusId       *int64        `json:"quoted_status_id"`
	QuotedStatusIdStr    *string       `json:"quoted_status_id_str"`
	IsQuoteStatus        bool          `json:"is_quote_status"`
	QuotedStatus         *QuotedStatus `json:"quoted_status"`
	RetweetedStatus      interface{}   `json:"retweeted_status"`
	QuoteCount           int           `json:"quote_count"`
	ReplyCount           int           `json:"reply_count"`
	RetweetCount         int           `json:"retweet_count"`
	FavoriteCount        int           `json:"favorite_count"`
	ViewsCount           int           `json:"views_count"`
	BookmarkCount        int           `json:"bookmark_count"`
	Lang                 string        `json:"lang"`
	IsPinned             bool          `json:"is_pinned"`
	ArticleTitle         string        `json:"article_title,omitempty"`
	ArticlePreviewText   string        `json:"article_preview_text,omitempty"`
	ArticleCoverURL      string        `json:"article_cover_url,omitempty"`
	ArticleJSON          db.JSONB      `json:"article_json,omitempty"`
	BulletPoints         db.JSONB      `json:"bullet_points,omitempty"`
	// AI Category fields
	IsProductUpdate        bool `json:"is_product_update,omitempty"`
	IsBusinessData         bool `json:"is_business_data,omitempty"`
	IsEcosystemPartnership bool `json:"is_ecosystem_partnership,omitempty"`
	IsProfitOpportunity    bool `json:"is_profit_opportunity,omitempty"`
	IsIndustryEvent        bool `json:"is_industry_event,omitempty"`
	IsOthers               bool `json:"is_other,omitempty"`
}

// User represents a Twitter user from the Twitter API
type User struct {
	Id                   int64     `json:"id"`
	IdStr                string    `json:"id_str"`
	Name                 string    `json:"name"`
	ScreenName           string    `json:"screen_name"`
	Location             string    `json:"location"`
	Url                  *string   `json:"url"`
	Description          string    `json:"description"`
	Protected            bool      `json:"protected"`
	Verified             bool      `json:"verified"`
	FollowersCount       int       `json:"followers_count"`
	FriendsCount         int       `json:"friends_count"`
	ListedCount          int       `json:"listed_count"`
	FavouritesCount      int       `json:"favourites_count"`
	StatusesCount        int       `json:"statuses_count"`
	CreatedAt            time.Time `json:"created_at"`
	ProfileBannerUrl     *string   `json:"profile_banner_url"`
	ProfileImageUrlHttps string    `json:"profile_image_url_https"`
	CanDm                bool      `json:"can_dm"`
}

// Entities represents entities in a tweet
type Entities struct {
	Media    []Media `json:"media,omitempty"`
	URLs     []URL   `json:"urls"`
	Hashtags []struct {
		Indices []int  `json:"indices"`
		Text    string `json:"text"`
	} `json:"hashtags"`
	Symbols []struct {
		Indices []int  `json:"indices"`
		Text    string `json:"text"`
	} `json:"symbols"`
	Timestamps   []interface{} `json:"timestamps"`
	UserMentions []struct {
		IdStr      string `json:"id_str"`
		Indices    []int  `json:"indices"`
		Name       string `json:"name"`
		ScreenName string `json:"screen_name"`
	} `json:"user_mentions"`
}

type QuotedStatus struct {
	TweetCreatedAt       time.Time   `json:"tweet_created_at"`
	Id                   int64       `json:"id"`
	IdStr                string      `json:"id_str"`
	Type                 string      `json:"type"`
	ConversationIdStr    string      `json:"conversation_id_str"`
	CommunityIdStr       interface{} `json:"community_id_str"`
	CommunityName        interface{} `json:"community_name"`
	Text                 interface{} `json:"text"`
	FullText             string      `json:"full_text"`
	Source               string      `json:"source"`
	Truncated            bool        `json:"truncated"`
	InReplyToStatusId    interface{} `json:"in_reply_to_status_id"`
	InReplyToStatusIdStr interface{} `json:"in_reply_to_status_id_str"`
	InReplyToUserId      interface{} `json:"in_reply_to_user_id"`
	InReplyToUserIdStr   interface{} `json:"in_reply_to_user_id_str"`
	InReplyToScreenName  interface{} `json:"in_reply_to_screen_name"`
	User                 User        `json:"user"`
	QuotedStatusId       interface{} `json:"quoted_status_id"`
	QuotedStatusIdStr    interface{} `json:"quoted_status_id_str"`
	IsQuoteStatus        bool        `json:"is_quote_status"`
	QuotedStatus         interface{} `json:"quoted_status"`
	RetweetedStatus      interface{} `json:"retweeted_status"`
	QuoteCount           int         `json:"quote_count"`
	ReplyCount           int         `json:"reply_count"`
	RetweetCount         int         `json:"retweet_count"`
	FavoriteCount        int         `json:"favorite_count"`
	ViewsCount           int         `json:"views_count"`
	BookmarkCount        int         `json:"bookmark_count"`
	Lang                 string      `json:"lang"`
	Entities             Entities    `json:"entities"`
	IsPinned             bool        `json:"is_pinned"`
}

// Media represents media in a tweet
type Media struct {
	AllowDownloadStatus struct {
		AllowDownload bool `json:"allow_download"`
	} `json:"allow_download_status,omitempty"`
	DisplayUrl           string `json:"display_url"`
	ExpandedUrl          string `json:"expanded_url"`
	ExtMediaAvailability struct {
		Status string `json:"status"`
	} `json:"ext_media_availability"`
	Features struct {
		Large struct {
			Faces []struct {
				H int `json:"h"`
				W int `json:"w"`
				X int `json:"x"`
				Y int `json:"y"`
			} `json:"faces"`
		} `json:"large"`
	} `json:"features,omitempty"`
	IdStr         string `json:"id_str"`
	Indices       []int  `json:"indices"`
	MediaKey      string `json:"media_key"`
	MediaUrlHttps string `json:"media_url_https"`
	OriginalInfo  struct {
		FocusRects []struct {
			H int `json:"h"`
			W int `json:"w"`
			X int `json:"x"`
			Y int `json:"y"`
		} `json:"focus_rects"`
		Height int `json:"height"`
		Width  int `json:"width"`
	} `json:"original_info"`
	Sizes struct {
		Large struct {
			H int `json:"h"`
			W int `json:"w"`
		} `json:"large"`
	} `json:"sizes"`
	Type                string `json:"type"`
	Url                 string `json:"url"`
	AdditionalMediaInfo struct {
		Monetizable bool `json:"monetizable"`
	} `json:"additional_media_info,omitempty"`
	VideoInfo struct {
		AspectRatio    []int `json:"aspect_ratio"`
		DurationMillis int   `json:"duration_millis"`
		Variants       []struct {
			ContentType string `json:"content_type"`
			Url         string `json:"url"`
			Bitrate     int    `json:"bitrate,omitempty"`
		} `json:"variants"`
	} `json:"video_info,omitempty"`
}

// URL represents a URL in a tweet
type URL struct {
	DisplayUrl  string `json:"display_url"`
	ExpandedUrl string `json:"expanded_url"`
	Indices     []int  `json:"indices"`
	Url         string `json:"url"`
}

type ProjectTweetAiINfo struct {
	IsImportant bool `json:"is_important"`
	Categories  struct {
		IsProductUpdate        bool `json:"is_product_update,omitempty"`
		IsBusinessData         bool `json:"is_business_data,omitempty"`
		IsEcosystemPartnership bool `json:"is_ecosystem_partnership,omitempty"`
		IsProfitOpportunity    bool `json:"is_profit_opportunity,omitempty"`
		IsIndustryEvent        bool `json:"is_industry_event,omitempty"`
	} `json:"categories"`
}

// NewTwitterService creates a new TwitterService
func NewTwitterService(cfg config.SocialDataConfig, database *db.Database, aiService *ai.Service, telegramService *TelegramService, redisService *RedisService, lockManager *DistributedLockManager, duplicateDetectorConfig config.DuplicateDetectorConfig) *TwitterService {
	duplicateDetector := NewDuplicateDetector(duplicateDetectorConfig, database)

	return &TwitterService{
		Config:                 cfg,
		db:                     database,
		aiService:              aiService,
		telegramService:        telegramService,
		redisService:           redisService,
		lockManager:            lockManager,
		duplicateDetector:      duplicateDetector,
		httpClient:             &http.Client{Timeout: 30 * time.Second},
		stopChan:               make(chan struct{}),
		periodicUpdateStopChan: make(chan struct{}),
		rateLimiter:            NewRateLimiter(cfg.RequestsPerSec, time.Second),
	}
}

// SetCAService sets the CAService
func (s *TwitterService) SetCAService(caService *CAService) {
	s.caService = caService
}

// Helper function to make API requests with rate limiting and error handling
func (s *TwitterService) makeAPIRequest(endpoint string, target interface{}) error {
	s.rateLimiter.Wait() // Wait for rate limiter

	reqURL := fmt.Sprintf("%s%s", s.Config.BaseURL, endpoint)
	log.Debug().Str("url", reqURL).Msg("Making API request")
	req, err := http.NewRequest("GET", reqURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+s.Config.APIKey)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		log.Error().Str("url", reqURL).Int("status_code", resp.StatusCode).Bytes("body", body).Msg("API request failed")
		return fmt.Errorf("API request to %s failed with status %d: %s", endpoint, resp.StatusCode, string(body))
	}

	if err := json.Unmarshal(body, target); err != nil {
		log.Error().Err(err).Bytes("body", body).Msg("Failed to unmarshal API response")
		return fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return nil
}

// fetchTweetDetailsFromAPI fetches complete tweet details by its ID from the SocialData API.
func (s *TwitterService) fetchTweetDetailsFromAPI(tweetID string) (*Tweet, error) {
	endpoint := fmt.Sprintf("/twitter/tweets/%s", tweetID)
	var tweet Tweet // This is services.Tweet, not db.Tweet
	err := s.makeAPIRequest(endpoint, &tweet)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch tweet %s: %w", tweetID, err)
	}
	return &tweet, nil
}

// processTweetsForPeriodicUpdate fetches and updates tweets based on configured criteria.
func (s *TwitterService) processTweetsForPeriodicUpdate() {
	log.Info().Msg("Starting periodic tweet update process...")

	var tweetsToUpdate []db.Tweet
	now := time.Now()
	olderThanThreshold := now.Add(-time.Duration(s.Config.PeriodicUpdateOlderThanHours) * time.Hour)
	newerThanThreshold := now.Add(-time.Duration(s.Config.PeriodicUpdateNewerThanHours) * time.Hour)
	updateInterval := time.Duration(s.Config.PeriodicUpdateIntervalHours) * time.Hour

	// Query for tweets that meet the criteria for update
	// 1. ContentType is not empty AND not NULL
	// 2. AIJudgment is "YES"
	// 3. IngestedAt is not older than 'PeriodicUpdateOlderThanHours'
	query := s.db.DB.Where("content_type IS NOT NULL AND tweet_type <> 'reply' AND content_type != '' AND ai_judgment = ? AND ingested_at > ?", "YES", olderThanThreshold)

	if err := query.Find(&tweetsToUpdate).Error; err != nil {
		log.Error().Err(err).Msg("Error querying tweets for periodic update")
		return
	}

	log.Info().Int("candidate_count", len(tweetsToUpdate)).Msg("Found candidate tweets for potential periodic update.")
	updatedCount := 0

	for _, tweet := range tweetsToUpdate {
		shouldUpdate := false
		if tweet.PeriodicallyUpdatedAt == nil {
			// Never updated by this process before
			// Update if (ingested newer than 'newerThanThreshold') OR (older than 'newerThanThreshold' but younger than 'olderThanThreshold' - for initial update)
			shouldUpdate = true
			log.Debug().Str("tweet_id", tweet.TweetID).Time("ingested_at", tweet.IngestedAt).Msg("Tweet never periodically updated. Scheduling update.")
		} else if tweet.IngestedAt.After(newerThanThreshold) {
			// Already updated, but it's a recent tweet, check if an interval has passed
			if now.Sub(*tweet.PeriodicallyUpdatedAt) > updateInterval {
				shouldUpdate = true
				log.Debug().Str("tweet_id", tweet.TweetID).Time("ingested_at", tweet.IngestedAt).Interface("last_updated_at", tweet.PeriodicallyUpdatedAt).Msgf("Tweet is newer than %d hours and update interval (%v) has passed. Scheduling update.", s.Config.PeriodicUpdateNewerThanHours, updateInterval)
			}
		} else {
			// Older than 'newerThanThreshold', already updated once. We skip further updates for these for now based on current simple logic.
			log.Debug().Str("tweet_id", tweet.TweetID).Time("ingested_at", tweet.IngestedAt).Interface("last_updated_at", tweet.PeriodicallyUpdatedAt).Msgf("Tweet is older than %d hours and already periodically updated. Skipping.", s.Config.PeriodicUpdateNewerThanHours)
		}

		if shouldUpdate {
			log.Info().Str("tweet_id", tweet.TweetID).Msg("Periodically updating tweet")
			apiTweet, err := s.fetchTweetDetailsFromAPI(tweet.TweetID) // apiTweet is services.Tweet
			if err != nil {
				log.Error().Err(err).Str("tweet_id", tweet.TweetID).Msg("Error fetching details for tweet during periodic update")
				continue // Skip to the next tweet
			}

			_, aiJudgment := s.aiCheckTweet(apiTweet, tweet.SourceListType)
			if aiJudgment != "YES" {
				log.Warn().Str("tweet_id", tweet.TweetID).Msg("AI judgment is NO, skipping periodic update")
				continue // Skip to the next tweet
			}

			s.updateTweetAndUser(aiJudgment, *apiTweet, tweet.SourceListType)

			// Update PeriodicallyUpdatedAt timestamp
			currentTime := time.Now()
			if err := s.db.Model(&db.Tweet{}).Where("id = ?", tweet.ID).Update("periodically_updated_at", &currentTime).Error; err != nil {
				log.Error().Err(err).Str("tweet_id", tweet.TweetID).Msg("Error updating PeriodicallyUpdatedAt for tweet")
				// Continue, as the main update succeeded
			}
			updatedCount++
			log.Info().Str("tweet_id", tweet.TweetID).Msg("Successfully updated tweet periodically.")
		}
	}
	log.Info().Int("updated_count", updatedCount).Msg("Periodic tweet update process finished.")
}

// startPeriodicTweetUpdater starts a goroutine that periodically processes tweets for updates.
func (s *TwitterService) startPeriodicTweetUpdater() {
	s.periodicUpdateWg.Add(1)
	go func() {
		defer s.periodicUpdateWg.Done()
		log.Info().Msgf("Starting periodic tweet updater. RunOnceOnly: %t, UpdateIntervalHours: %d, NewerThanHours: %d, OlderThanHours: %d",
			s.Config.PeriodicUpdateRunOnceOnly,
			s.Config.PeriodicUpdateIntervalHours,
			s.Config.PeriodicUpdateNewerThanHours,
			s.Config.PeriodicUpdateOlderThanHours)

		// Run once immediately
		s.processTweetsForPeriodicUpdate()

		// If run_once_only is true, exit after the first run
		if s.Config.PeriodicUpdateRunOnceOnly {
			log.Info().Msg("Periodic update configured to run once only. Exiting updater.")
			return
		}

		// Use a fixed ticker interval to check for updates. The actual logic for whether to update
		// a specific tweet is handled within processTweetsForPeriodicUpdate based on its individual state
		// and the configured PeriodicUpdateIntervalHours.
		ticker := time.NewTicker(time.Duration(s.Config.PeriodicUpdateIntervalHours) * time.Minute) // Check eligibility every 15 minutes
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				s.processTweetsForPeriodicUpdate()
			case <-s.periodicUpdateStopChan:
				log.Info().Msg("Periodic tweet updater received stop signal, shutting down.")
				return
			}
		}
	}()
}

// StartMonitoring starts the Twitter monitoring process
func (s *TwitterService) StartMonitoring() {
	// Check if AI re-classification is enabled and run it on startup
	if s.Config.AIReclassifyEnabled {
		log.Info().Msg("AI re-classification feature is enabled. Starting re-classification of tweets with is_others = true.")
		go s.ReclassifyIsOthersTweets() // Run in background to avoid blocking startup
	} else {
		log.Info().Msg("AI re-classification feature is disabled.")
	}

	if s.Config.PeriodicUpdateEnabled {
		log.Info().Msg("Periodic tweet update feature is enabled. Starting updater.")
		s.startPeriodicTweetUpdater() // This will manage its own wg via periodicUpdateWg
	} else {
		log.Info().Msg("Periodic tweet update feature is disabled.")
	}

	s.wg.Add(1) // For the main monitorTwitter goroutine
	go s.monitorTwitter()
}

// StopMonitoring stops the Twitter monitoring process and the periodic updater
func (s *TwitterService) StopMonitoring() {
	log.Info().Msg("Stopping Twitter monitoring...")
	close(s.stopChan) // Signal main monitor to stop
	s.wg.Wait()       // Wait for main monitor to finish
	log.Info().Msg("Twitter monitoring stopped.")

	if s.Config.PeriodicUpdateEnabled {
		log.Info().Msg("Stopping periodic tweet updater...")
		close(s.periodicUpdateStopChan) // Signal periodic updater to stop
		s.periodicUpdateWg.Wait()       // Wait for periodic updater to finish
		log.Info().Msg("Periodic tweet updater stopped.")
	}
}

// monitorTwitter continuously monitors Twitter for new tweets
func (s *TwitterService) monitorTwitter() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.Config.PollingInterval)
	defer ticker.Stop()

	// Initial fetch with configurable lookback
	var lookbackPtr *int
	if s.Config.InitialLookbackHours > 0 {
		initialLookback := s.Config.InitialLookbackHours
		lookbackPtr = &initialLookback
		log.Info().Int("lookback_hours", initialLookback).Msg("Using configured initial lookback")
	}

	// Fetch tweets from configured Twitter lists
	for _, list := range s.Config.TwitterLists {
		log.Info().Str("list_id", list.ID).Str("list_type", list.Type).Msg("Fetching tweets from Twitter list")
		s.FetchListTweets(list.ID, list.Type, lookbackPtr)
	}

	s.FetchTweets(lookbackPtr)

	if !s.Config.WebhookEnabled {
		for {
			select {
			case <-ticker.C:
				// Regular polling without lookback
				log.Info().Msg("Webhook disabled, starting polling for tweets")
				// Fetch tweets from configured Twitter lists
				for _, list := range s.Config.TwitterLists {
					s.FetchListTweets(list.ID, list.Type, nil)
				}
				s.FetchTweets(nil)
			case <-s.stopChan:
				log.Print("Stopping Twitter monitoring")
				return
			}
		}
	}
}

// FetchTweets fetches tweets from the Twitter API
// If lookbackHours is provided, it will fetch tweets from the past lookbackHours hours
// by paginating through results using the cursor until reaching tweets older than the lookback period
func (s *TwitterService) FetchTweets(lookbackHours *int) {
	start := time.Now()

	// Determine cutoff time if lookback is specified
	var cutoffTime time.Time
	if lookbackHours != nil {
		cutoffTime = time.Now().Add(time.Duration(-*lookbackHours) * time.Hour)
		log.Info().
			Int("lookback_hours", *lookbackHours).
			Time("cutoff_time", cutoffTime).
			Msg("Fetching tweets with lookback period")
	}

	// Check if we have any keywords configured
	if len(s.Config.SearchKeywords) == 0 {
		log.Error().Msg("No search keywords configured")
		return
	}

	// Process each keyword
	var totalTweetCount int

	for _, keyword := range s.Config.SearchKeywords {
		log.Info().Str("keyword", keyword).Msg("Processing search keyword")

		// Build the query for this keyword
		query := url.QueryEscape(keyword)
		apiURL := fmt.Sprintf("%s/twitter/search?query=%s&type=Latest", s.Config.BaseURL, query)

		var cursor string
		var keywordTweetCount int

		// Continue fetching until we reach the cutoff time or there are no more pages
		for {
			// Add cursor to URL if we have one
			currentURL := apiURL
			if cursor != "" {
				currentURL = fmt.Sprintf("%s&cursor=%s", apiURL, cursor)
			}

			// Wait for rate limiter
			s.rateLimiter.Wait()

			// Create request
			req, err := http.NewRequest("GET", currentURL, nil)
			if err != nil {
				log.Error().Err(err).Msg("Error creating request")
				return
			}

			// Add authorization header
			req.Header.Add("Authorization", "Bearer "+s.Config.APIKey)

			// Record API request metrics
			apiStart := time.Now()

			// Send request
			resp, err := s.httpClient.Do(req)
			if err != nil {
				log.Error().Err(err).Msg("Error fetching tweets")
				metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/search", "error").Inc()
				return
			}
			defer resp.Body.Close()

			// Record API request duration
			apiDuration := time.Since(apiStart)
			metrics.ExternalAPIRequestDuration.WithLabelValues("socialdata", "twitter/search").Observe(apiDuration.Seconds())

			// Check for rate limiting
			if resp.StatusCode == http.StatusTooManyRequests {
				retryAfter := resp.Header.Get("Retry-After")
				if retryAfter != "" {
					var seconds time.Duration
					seconds, err = time.ParseDuration(retryAfter + "s")
					if err == nil {
						log.Warn().
							Dur("retry_after", seconds).
							Msg("Rate limited by SocialData API")
						time.Sleep(seconds)
					}
				}
				metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/search", "rate_limited").Inc()
				return
			}

			// Check for other errors
			if resp.StatusCode != http.StatusOK {
				body, _ := io.ReadAll(resp.Body)
				log.Error().
					Int("status_code", resp.StatusCode).
					Str("response", string(body)).
					Msg("Error response from Twitter API")
				metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/search", "error").Inc()
				return
			}

			// Record successful API request
			metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/search", "success").Inc()

			// Parse response
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				log.Error().Err(err).Msg("Error reading response body")
				return
			}

			var searchResponse TwitterSearchResponse
			if err := json.Unmarshal(body, &searchResponse); err != nil {
				log.Error().Err(err).Msg("Error parsing response")
				return
			}

			// Process tweets
			tweetCount := len(searchResponse.Tweets)
			keywordTweetCount += tweetCount
			totalTweetCount += tweetCount

			if tweetCount > 0 {
				log.Info().
					Int("count", tweetCount).
					Str("keyword", keyword).
					Bool("has_cursor", searchResponse.Cursor != "").
					Msg("Found tweets matching keyword")
			} else {
				log.Debug().Str("keyword", keyword).Msg("No new tweets found")
				break // No tweets in this page, stop fetching
			}

			var reachedCutoff bool
			for _, tweet := range searchResponse.Tweets {
				// Check if we've reached the cutoff time
				if lookbackHours != nil && tweet.TweetCreatedAt.Before(cutoffTime) {
					log.Info().
						Str("tweet_id", tweet.IdStr).
						Time("tweet_time", tweet.TweetCreatedAt).
						Time("cutoff_time", cutoffTime).
						Msg("Reached cutoff time, stopping tweet fetch")
					reachedCutoff = true
					break
				}
				s.ProcessTweet(tweet, keyword)
			}

			if lookbackHours == nil {
				reachedCutoff = true
			}

			// Stop if we've reached the cutoff time or there's no cursor for the next page
			if reachedCutoff || searchResponse.Cursor == "" {
				break
			}

			// Update cursor for next page
			cursor = searchResponse.Cursor

			// Add a small delay between requests to be nice to the API
			time.Sleep(500 * time.Millisecond)
		}

		// Record duration for this keyword
		keywordDuration := time.Since(start)
		log.Debug().
			Dur("duration", keywordDuration).
			Int("keyword_tweet_count", totalTweetCount).
			Str("keyword", keyword).
			Msg("Finished fetching tweets for keyword")
	}

	// Record total duration for all keywords
	totalDuration := time.Since(start)
	log.Info().
		Dur("total_duration", totalDuration).
		Int("total_tweet_count", totalTweetCount).
		Int("keyword_count", len(s.Config.SearchKeywords)).
		Msg("Finished fetching tweets for all keywords")
}

// Regular expressions for contract addresses
var (
	evmRegex    = regexp.MustCompile(`0x[a-fA-F0-9]{40}`)
	solanaRegex = regexp.MustCompile(`[a-zA-Z0-9]{32,44}`)
)

// updateTweetAndUser updates an existing tweet and its associated user information
func (s *TwitterService) updateTweetAndUser(aiJudgment string, tweet Tweet, keyword string) {
	// Update the Twitter user information
	dbUser := &db.TwitterUser{
		UserID:          tweet.User.IdStr,
		ScreenName:      tweet.User.ScreenName,
		Name:            tweet.User.Name,
		FollowersCount:  tweet.User.FollowersCount,
		IsVerified:      tweet.User.Verified,
		ProfileImageURL: tweet.User.ProfileImageUrlHttps,
		FetchedAt:       time.Now(),
	}

	dbStart := time.Now()
	if err := s.db.SaveTwitterUser(dbUser); err != nil {
		log.Error().
			Err(err).
			Str("user_id", dbUser.UserID).
			Msg("Error updating Twitter user")
		metrics.DatabaseOperationsTotal.WithLabelValues("update", "twitter_users", "error").Inc()
	} else {
		metrics.DatabaseOperationsTotal.WithLabelValues("update", "twitter_users", "success").Inc()
		metrics.DatabaseOperationDuration.WithLabelValues("update", "twitter_users").Observe(time.Since(dbStart).Seconds())
	}

	// Update the tweet with new information (views count, etc.)
	// Create a map to convert the tweet to JSON
	tweetJSON := db.JSONB{}
	tweetBytes, err := json.Marshal(tweet)
	if err != nil {
		log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error marshaling tweet to JSON")
	} else {
		if err := json.Unmarshal(tweetBytes, &tweetJSON); err != nil {
			log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error unmarshaling tweet to JSONB")
		}
	}

	var existTweet db.Tweet
	// Use GORM to check for the tweet
	if err := s.db.Where("tweet_id = ?", tweet.IdStr).First(&existTweet).Error; err != nil {
		log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error checking if tweet exists")
		return
	}

	dbTweet := existTweet
	if aiJudgment == "YES" {
		dbTweet.IsProductUpdate = tweet.IsProductUpdate
		dbTweet.IsBusinessData = tweet.IsBusinessData
		dbTweet.IsEcosystemPartnership = tweet.IsEcosystemPartnership
		dbTweet.IsProfitOpportunity = tweet.IsProfitOpportunity
		dbTweet.IsIndustryEvent = tweet.IsIndustryEvent
		dbTweet.IsOthers = tweet.IsOthers
	}
	dbTweet.TextContent = tweet.FullText
	dbTweet.ReplyCount = tweet.ReplyCount
	dbTweet.RetweetCount = tweet.RetweetCount
	dbTweet.BookmarkCount = tweet.BookmarkCount
	dbTweet.FullTweetJSON = tweetJSON
	dbTweet.ViewsCount = tweet.ViewsCount
	dbTweet.FavoriteCount = tweet.FavoriteCount

	if tweet.BulletPoints != nil {
		dbTweet.BulletPoints = tweet.BulletPoints
	}

	dbTweet.IngestedAt = time.Now()

	// 保存 Tweet 基础信息
	dbStart = time.Now()
	if err := s.db.SaveTweet(&dbTweet); err != nil {
		log.Error().
			Err(err).
			Str("tweet_id", dbTweet.TweetID).
			Msg("Error updating tweet")
		metrics.DatabaseOperationsTotal.WithLabelValues("update", "tweets", "error").Inc()
		return
	}
	metrics.DatabaseOperationsTotal.WithLabelValues("update", "tweets", "success").Inc()
	metrics.DatabaseOperationDuration.WithLabelValues("update", "tweets").Observe(time.Since(dbStart).Seconds())

	if keyword != "" {
		// 创建或获取标签，并关联到 Tweet
		tag, err := s.db.GetOrCreateTag(keyword)
		if err != nil {
			log.Error().Err(err).Str("keyword", keyword).Msg("Error creating tag")
			return
		}

		// 关联标签与推文
		if err := s.db.AssociateTweetWithTags(dbTweet.ID, []db.Tag{*tag}); err != nil {
			log.Error().Err(err).Str("tweet_id", dbTweet.TweetID).Str("tag", keyword).Msg("Error associating tag with tweet")
		}
	}
}

// ProcessTweet processes a single tweet
func (s *TwitterService) ProcessTweet(tweet Tweet, keyword string) {
	start := time.Now()

	keyword = strings.ToLower(keyword)

	// Check if user is in blacklist
	for _, blacklistedUser := range s.Config.BlacklistedUsers {
		if tweet.User.ScreenName == blacklistedUser {
			log.Debug().Str("tweet_id", tweet.IdStr).Str("user", blacklistedUser).Msg("skipping blacklisted user tweet")
			return
		}
	}

	// Check if tweet ID already exists in the database
	existTweet, exists, err := s.db.TweetExistsWithTweet(tweet.IdStr)
	if err != nil {
		log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error checking if tweet exists")
		return
	}

	// If tweet already exists, we need to:
	// 1. Update its basic info (like ViewsCount) and the user info
	// 2. Add the new tag to the tweet
	// 3. Potentially update the CA tags if this tweet has associated CAs
	if exists {
		log.Debug().Str("tweet_id", tweet.IdStr).Str("keyword", keyword).Msg("Tweet already exists in database, updating it and adding new tag")

		// Update basic information on tweets
		s.updateTweetAndUser("NO", tweet, keyword)

		// Get existing tweet, including its associated CAs
		var dbTweet db.Tweet
		if err = s.db.Preload("ExtractedCAs").Where("tweet_id = ?", tweet.IdStr).First(&dbTweet).Error; err != nil {
			log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error fetching existing tweet with CAs")
			return
		}

		// If the tweet has associated CAs, add the new tag to them
		if len(dbTweet.ExtractedCAs) > 0 {
			// Get or create the tag for the current keyword
			tag, err := s.db.GetOrCreateTag(keyword)
			if err != nil {
				log.Error().Err(err).Str("keyword", keyword).Msg("Error creating tag for existing CAs")
				return
			}

			// Add the new tag to each associated CA
			for _, ca := range dbTweet.ExtractedCAs {
				if err := s.db.AssociateCAWithTags(ca.ID, []db.Tag{*tag}); err != nil {
					log.Error().Err(err).Str("ca_address", ca.CAAddress).Str("tag", keyword).Msg("Error associating tag with existing CA")
				}
			}
		}
		if existTweet.ContainsTargetKeyword {
			return
		}
	}

	// Check if tweet is of type article
	for _, url := range tweet.Entities.URLs {
		if strings.Contains(url.ExpandedUrl, "x.com/i/article/") {
			tweet.Type = "article"
			idStr := tweet.IdStr
			articleData, err := s.fetchArticleData(idStr)
			if err != nil {
				log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error fetching article data")
			} else {
				articleJSON, err := json.Marshal(articleData)
				if err != nil {
					log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error marshaling article data")
				} else {
					var jsonbData db.JSONB
					if err := json.Unmarshal(articleJSON, &jsonbData); err != nil {
						log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error unmarshaling article data to JSONB")
						tweet.ArticleJSON = db.JSONB{}
					} else {
						tweet.ArticleJSON = jsonbData
					}
				}
				if articleMap, ok := articleData["article"].(map[string]interface{}); ok {
					if title, ok := articleMap["title"].(string); ok {
						tweet.ArticleTitle = title
					}
					if previewText, ok := articleMap["preview_text"].(string); ok {
						tweet.ArticlePreviewText = previewText
					}
					if coverURL, ok := articleMap["cover_url"].(string); ok {
						tweet.ArticleCoverURL = coverURL
					}
				}
			}
			break
		}
	}

	// Keyword contains judgment logic
	var recognizedCAs []db.RecognizedCA
	keywordParts := strings.Fields(keyword)
	var requiredParts []string
	for _, part := range keywordParts {
		if part != "ca" && part != "" {
			requiredParts = append(requiredParts, strings.ToLower(part))
		}
	}
	containsAll := true
	lowerText := strings.ToLower(tweet.FullText)
	for _, part := range requiredParts {
		if !strings.Contains(lowerText, part) {
			containsAll = false
			break
		}
	}
	if !containsAll {
		recognizedCAs = []db.RecognizedCA{}
	} else {

		// 1. Try with evm regex
		evmAddresses := evmRegex.FindAllString(tweet.FullText, -1)
		for _, addr := range evmAddresses {
			// Validate EIP-55 checksum for evm addresses
			addr, _ = utils.ToChecksumAddress(addr)
			if addr == "" {
				continue
			}
			recognizedCAs = append(recognizedCAs, db.RecognizedCA{CAAddress: addr, ChainType: "evm"})
		}

		// 2. Try with solana regex
		// Ensure solana regex doesn't overlap with already found evm addresses if text can be ambiguous (unlikely for distinct patterns)
		solanaAddresses := solanaRegex.FindAllString(tweet.FullText, -1)
		for _, addr := range solanaAddresses {
			// Avoid adding if it's already identified as evm (though patterns are distinct)
			isEVM := false
			for _, existingCA := range recognizedCAs {
				if existingCA.CAAddress == addr && existingCA.ChainType == "evm" {
					isEVM = true
					break
				}
			}
			if !isEVM {
				// Validate Solana address format
				solanaAddr := s.tryToGetSolanaAddress(addr)
				if solanaAddr == "" {
					continue
				}
				recognizedCAs = append(recognizedCAs, db.RecognizedCA{CAAddress: solanaAddr, ChainType: "solana"})
			}
		}

		// 3. If no addresses found by regex, try AI service
		if len(recognizedCAs) == 0 && s.aiService != nil {
			log.Debug().Str("tweet_id", tweet.IdStr).Msg("No CAs found by regex, trying AI detection")

			// Add timeout control
			aiReq := &ai.ContractDetectionRequest{
				TweetID:   tweet.IdStr,
				TweetText: tweet.FullText,
				// TweetAuthor: tweet.User.ScreenName,
			}
			detectedAIAddresses, err := s.aiService.DetectContractAddresses(context.Background(), aiReq)
			if err != nil {
				log.Warn().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error detecting contract addresses with AI")
				// Do not return, proceed with saving tweet without AI-detected CAs
			} else {
				for _, aiAddr := range detectedAIAddresses {
					log.Info().
						Str("tweet_id", tweet.IdStr).
						Str("address", aiAddr.Address).
						Str("chain_type", aiAddr.ChainType).
						Float64("confidence", aiAddr.Confidence).
						Msg("Contract address detected by AI")
					switch aiAddr.ChainType {
					case "evm":
						// Validate EIP-55 checksum for evm addresses
						aiAddr.Address, _ = utils.ToChecksumAddress(aiAddr.Address)
						if aiAddr.Address == "" {
							continue
						}
					case "solana":
						// Validate Solana address format
						solanaAddr := s.tryToGetSolanaAddress(aiAddr.Address)
						if solanaAddr == "" {
							continue
						}
						aiAddr.Address = solanaAddr
					default:
						continue
					}
					recognizedCAs = append(recognizedCAs, db.RecognizedCA{
						CAAddress:      aiAddr.Address,
						ChainType:      aiAddr.ChainType,
						AddedAt:        time.Now(),
						ReferenceCount: 0,
					})
				}
			}
		}
	}

	if len(recognizedCAs) == 0 {
		log.Debug().Str("tweet_id", tweet.IdStr).Msg("No CAs found")
		if exists {
			return
		}
	}

	// Increment tweets processed counter
	metrics.TweetsProcessed.Inc()

	// Save the user
	user := &db.TwitterUser{
		UserID:          tweet.User.IdStr,
		ScreenName:      tweet.User.ScreenName,
		Name:            tweet.User.Name,
		FollowersCount:  tweet.User.FollowersCount,
		IsVerified:      tweet.User.Verified,
		ProfileImageURL: tweet.User.ProfileImageUrlHttps,
	}

	dbStart := time.Now()
	if err = s.db.SaveTwitterUser(user); err != nil {
		log.Error().
			Err(err).
			Str("user_id", user.UserID).
			Str("screen_name", user.ScreenName).
			Msg("Error saving user")
		metrics.DatabaseOperationsTotal.WithLabelValues("save", "twitter_users", "error").Inc()
		return
	}
	metrics.DatabaseOperationsTotal.WithLabelValues("save", "twitter_users", "success").Inc()
	metrics.DatabaseOperationDuration.WithLabelValues("save", "twitter_users").Observe(time.Since(dbStart).Seconds())

	// Convert the tweet to a JSON object
	tweetJSON := make(db.JSONB)
	tweetBytes, err := json.Marshal(tweet)
	if err != nil {
		log.Error().
			Err(err).
			Str("tweet_id", tweet.IdStr).
			Msg("Error marshaling tweet")
	} else {
		if err := json.Unmarshal(tweetBytes, &tweetJSON); err != nil {
			log.Error().
				Err(err).
				Str("tweet_id", tweet.IdStr).
				Msg("Error unmarshaling tweet to JSONB")
		}
	}

	// Save the tweet
	dbTweet := &db.Tweet{
		TweetID:               tweet.IdStr,
		UserIDFK:              tweet.User.IdStr,
		TextContent:           tweet.FullText,
		FullTweetJSON:         tweetJSON,
		PublishedAt:           tweet.TweetCreatedAt,
		ViewsCount:            tweet.ViewsCount,
		ReplyCount:            tweet.ReplyCount,
		RetweetCount:          tweet.RetweetCount,
		FavoriteCount:         tweet.FavoriteCount,
		BookmarkCount:         tweet.BookmarkCount,
		ContainsTargetKeyword: len(recognizedCAs) > 0,
		TweetType:             tweet.Type,
		ArticleTitle:          tweet.ArticleTitle,
		ArticlePreviewText:    tweet.ArticlePreviewText,
		ArticleCoverURL:       tweet.ArticleCoverURL,
		FullArticleJSON:       tweet.ArticleJSON,
	}

	dbStart = time.Now()
	if err := s.db.SaveTweet(dbTweet); err != nil {
		log.Error().
			Err(err).
			Str("tweet_id", dbTweet.TweetID).
			Msg("Error saving tweet")
		metrics.DatabaseOperationsTotal.WithLabelValues("save", "tweets", "error").Inc()
		return
	}
	metrics.DatabaseOperationsTotal.WithLabelValues("save", "tweets", "success").Inc()
	metrics.DatabaseOperationDuration.WithLabelValues("save", "tweets").Observe(time.Since(dbStart).Seconds())

	if keyword != "" {
		// 创建或获取标签，并关联到 Tweet
		tag, err := s.db.GetOrCreateTag(keyword)
		if err != nil {
			log.Error().Err(err).Str("keyword", keyword).Msg("Error creating tag")
			return
		}

		// 关联标签与推文
		if err := s.db.AssociateTweetWithTags(dbTweet.ID, []db.Tag{*tag}); err != nil {
			log.Error().Err(err).Str("tweet_id", dbTweet.TweetID).Str("tag", keyword).Msg("Error associating tag with tweet")
		}
	}

	// Extract and process contract addresses
	if s.caService != nil && dbTweet.ContainsTargetKeyword {
		dbTweet.ExtractedCAs = recognizedCAs
		s.caService.ProcessTweet(dbTweet)
	}

	log.Debug().
		Str("tweet_id", tweet.IdStr).
		Str("user", tweet.User.ScreenName).
		Int("ca count", len(dbTweet.ExtractedCAs)).
		Dur("duration", time.Since(start)).
		Msg("Processed tweet")
}

// GetTweets retrieves tweets from the database
func (s *TwitterService) GetTweets(limit, offset int) ([]*db.Tweet, error) {
	return s.db.GetTweets(limit, offset)
}

// GetTweetsByTags retrieves tweets from the database filtered by tags
// If tags is empty, it returns all tweets (same as GetTweets)
func (s *TwitterService) GetTweetsByTags(limit, offset int, tags []string) ([]*db.Tweet, error) {
	if len(tags) == 0 {
		// If no tags provided, fall back to regular GetTweets
		return s.GetTweets(limit, offset)
	}

	// Use the database method with tag filtering
	return s.db.GetTweetsByTags(limit, offset, tags)
}

// GetTweetByID retrieves a single tweet by its ID
func (s *TwitterService) GetTweetByID(tweetID string) (*db.Tweet, error) {
	// Use the database method to get tweet by ID
	return s.db.GetTweetByID(tweetID)
}

// GetListTweets retrieves AI Agent related tweets with filtering options
func (s *TwitterService) GetListTweets(req db.GetListTweetsRequest) ([]*db.Tweet, error) {
	// Convert to db request struct
	return s.db.GetListTweets(req)
}

// GetAnnouncementStatistics retrieves announcement statistics grouped by Twitter user for tweets with source_list_type="Projects" and ai_judgment="YES"
// Optional time filtering can be applied using startTime and endTime parameters (Unix timestamps)
func (s *TwitterService) GetAnnouncementStatistics(startTime, endTime *int64) ([]db.TwitterUserAnnouncementStats, error) {
	return s.db.GetAnnouncementStatistics(startTime, endTime)
}

// GetTelegramNotificationStats retrieves statistics about Telegram notifications
func (s *TwitterService) GetTelegramNotificationStats() (map[string]interface{}, error) {
	return s.db.GetTelegramNotificationStats()
}

// SaveCollectionTags saves collection tags to the database
func (s *TwitterService) SaveCollectionTags(collections []interface{}) error {
	return s.db.SaveCollectionTags(collections)
}

// GetTagsByTwitterUsername retrieves tags for a specific Twitter username
func (s *TwitterService) GetTagsByTwitterUsername(twitterUsername string) ([]string, error) {
	return s.db.GetTagsByTwitterUsername(twitterUsername)
}

// FetchListTweets fetches tweets from a specific Twitter list
func (s *TwitterService) FetchListTweets(listID, listType string, lookbackHours *int) {
	start := time.Now()

	// Determine cutoff time if lookback is specified
	var cutoffTime time.Time
	if lookbackHours != nil {
		cutoffTime = time.Now().Add(time.Duration(-*lookbackHours) * time.Hour)
		log.Info().
			Int("lookback_hours", *lookbackHours).
			Time("cutoff_time", cutoffTime).
			Str("list_id", listID).
			Msg("Fetching tweets from list with lookback period")
	}

	apiURL := fmt.Sprintf("%s/twitter/search?query=%s&type=Latest", s.Config.BaseURL, url.QueryEscape(fmt.Sprintf("list:%s -filter:replies", listID)))

	var cursor string
	var totalTweetCount int

	for {
		currentURL := apiURL
		if cursor != "" {
			currentURL = fmt.Sprintf("%s?cursor=%s", apiURL, cursor)
		}

		// Wait for rate limiter
		s.rateLimiter.Wait()

		// Create request
		req, err := http.NewRequest("GET", currentURL, nil)
		if err != nil {
			log.Error().Err(err).Msg("Error creating request for list tweets")
			return
		}

		// Add authorization header
		req.Header.Add("Authorization", "Bearer "+s.Config.APIKey)

		// Record API request metrics
		apiStart := time.Now()
		resp, err := s.httpClient.Do(req)
		if err != nil {
			log.Error().Err(err).Msg("Error fetching list tweets")
			metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", apiURL, "error").Inc()
			return
		}
		defer resp.Body.Close()

		// Record API request duration
		apiDuration := time.Since(apiStart)
		metrics.ExternalAPIRequestDuration.WithLabelValues("socialdata", apiURL).Observe(apiDuration.Seconds())

		// Check for rate limiting
		if resp.StatusCode == http.StatusTooManyRequests {
			retryAfter := resp.Header.Get("Retry-After")
			if retryAfter != "" {
				var seconds time.Duration
				seconds, err = time.ParseDuration(retryAfter + "s")
				if err == nil {
					log.Warn().
						Dur("retry_after", seconds).
						Msg("Rate limited by SocialData API for list tweets")
					time.Sleep(seconds)
				}
			}
			metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", apiURL, "rate_limited").Inc()
			return
		}

		// Check for other errors
		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			log.Error().
				Int("status_code", resp.StatusCode).
				Str("response", string(body)).
				Msg("Error response from Twitter List API")
			metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", apiURL, "error").Inc()
			return
		}

		// Record successful API request
		metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", apiURL, "success").Inc()

		// Parse response
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Error().Err(err).Msg("Error reading response body for list tweets")
			return
		}

		var searchResponse TwitterSearchResponse
		if err := json.Unmarshal(body, &searchResponse); err != nil {
			log.Error().Err(err).Msg("Error parsing response for list tweets")
			return
		}

		// Process tweets
		tweetCount := len(searchResponse.Tweets)
		totalTweetCount += tweetCount

		if tweetCount > 0 {
			log.Info().
				Int("count", tweetCount).
				Str("list_id", listID).
				Str("list_type", listType).
				Bool("has_cursor", searchResponse.Cursor != "").
				Msg("Found tweets from list")
		} else {
			log.Debug().Str("list_id", listID).Msg("No new tweets found in list")
			break // No tweets in this page, stop fetching
		}

		var reachedCutoff bool
		for _, tweet := range searchResponse.Tweets {
			// Check if we've reached the cutoff time
			if lookbackHours != nil && tweet.TweetCreatedAt.Before(cutoffTime) {
				log.Info().
					Str("tweet_id", tweet.IdStr).
					Time("tweet_time", tweet.TweetCreatedAt).
					Time("cutoff_time", cutoffTime).
					Msg("Reached cutoff time, stopping list tweet fetch")
				reachedCutoff = true
				break
			}
			// Process tweet with AI judgment
			s.ProcessListTweet(tweet, listType)
		}

		if lookbackHours == nil {
			reachedCutoff = true
		}

		// Stop if we've reached the cutoff time or there's no cursor for the next page
		if reachedCutoff || searchResponse.Cursor == "" {
			break
		}

		// Update cursor for next page
		cursor = searchResponse.Cursor

		// Add a small delay between requests to be nice to the API
		time.Sleep(500 * time.Millisecond)
	}

	// Record total duration for list tweets
	totalDuration := time.Since(start)
	log.Info().
		Dur("total_duration", totalDuration).
		Int("total_tweet_count", totalTweetCount).
		Str("list_id", listID).
		Str("list_type", listType).
		Msg("Finished fetching tweets for list")
}

// ProcessListTweet processes a single tweet from a list with AI judgment
func (s *TwitterService) ProcessListTweet(tweet Tweet, listType string) {
	if tweet.Type == "reply" {
		log.Debug().Str("tweet_id", tweet.IdStr).Str("list_type", listType).Msg("List tweet is a reply, skipping")
		return
	}
	if listType == "KOLs" && tweet.User.ScreenName != "scattering_io" {
		log.Debug().Str("tweet_id", tweet.IdStr).Str("list_type", listType).Msg("List tweet is not from scattering_io, skipping")
		return
	}

	start := time.Now()

	// Acquire distributed lock to prevent race conditions during concurrent tweet processing
	if s.lockManager != nil {
		lockKey := s.lockManager.CreateTweetLockKey(tweet.IdStr)
		lockOpts := &LockOptions{
			Timeout:    480 * time.Second, // Lock timeout
			RetryDelay: 60 * time.Second,
			MaxRetries: 5,
			Metadata:   fmt.Sprintf("ProcessListTweet:%s", listType),
		}

		lock, err := s.lockManager.AcquireLock(context.Background(), lockKey, lockOpts)
		if err != nil {
			log.Warn().
				Err(err).
				Str("tweet_id", tweet.IdStr).
				Str("list_type", listType).
				Str("lock_key", lockKey).
				Msg("Failed to acquire distributed lock for tweet processing, skipping")
			return
		}

		// Ensure lock is released when function exits
		defer func() {
			if releaseErr := lock.ReleaseLock(); releaseErr != nil {
				log.Error().
					Err(releaseErr).
					Str("tweet_id", tweet.IdStr).
					Str("lock_key", lockKey).
					Msg("Error releasing distributed lock")
			}
		}()

		log.Debug().
			Str("tweet_id", tweet.IdStr).
			Str("list_type", listType).
			Str("lock_key", lockKey).
			Msg("Successfully acquired distributed lock for tweet processing")
	}

	// Check if tweet ID already exists in the database
	existTweet, exists, err := s.db.TweetExistsWithTweet(tweet.IdStr)
	if err != nil {
		log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error checking if list tweet exists")
		return
	}

	// Variables to track state for deferred Telegram notification
	var shouldSendTelegramNotification bool
	var finalDBTweet *db.Tweet

	// Defer Telegram notification - will execute at function end regardless of early returns
	defer func() {
		if !shouldSendTelegramNotification {
			return
		}

		if finalDBTweet.BulletPoints == nil {
			log.Debug().Str("tweet_id", tweet.IdStr).Msg("No bullet points for tweet, skipping notification")
			return
		}

		if !s.telegramService.config.Enabled {
			log.Debug().Str("tweet_id", tweet.IdStr).Msg("Telegram is not enabled, skipping notification")
			return
		}

		if s.telegramService == nil {
			log.Error().Str("tweet_id", tweet.IdStr).Msg("Telegram service is nil, cannot send notification")
			return
		}

		if finalDBTweet == nil {
			log.Error().Str("tweet_id", tweet.IdStr).Msg("Final tweet is nil, cannot send Telegram notification")
			return
		}

		// Create copies for the notification to avoid race conditions
		tweetCopyForNotification := tweet
		dbTweetCopyForNotification := *finalDBTweet

		// Send notification asynchronously
		go s.sendTelegramNotification(tweetCopyForNotification, dbTweetCopyForNotification)

		log.Debug().
			Str("tweet_id", tweet.IdStr).
			Str("list_type", listType).
			Msg("Telegram notification sent via defer for important tweet")

	}()

	// If a tweet exists, update basic info
	if exists {
		log.Debug().Str("tweet_id", tweet.IdStr).Str("list_type", listType).Msg("List tweet already exists in database, updating it")
		s.updateTweetAndUser("NO", tweet, listType)

		// For "important" list type, we still want to potentially send notification
		// even if the tweet exists, as long as AI judgment indicates it's important
		if listType == "important" {
			// Get the AI judgment for this tweet to determine if notification should be sent

			// Set up notification state - we'll create a minimal dbTweet for notification
			if existTweet.AIJudgment == "YES" && existTweet.BulletPoints != nil {
				shouldSendTelegramNotification = true
				finalDBTweet = existTweet
			}
		}

		// If the tweet already has AI judgment, skip further processing
		if existTweet.AIJudgment == "YES" {
			return
		}
	}

	// Detect and mark duplicate content before AI processing
	if err := s.duplicateDetector.DetectAndMarkDuplicates(tweet.FullText, tweet.IdStr, tweet.User.IdStr); err != nil {
		log.Error().Err(err).
			Str("tweet_id", tweet.IdStr).
			Str("user_id", tweet.User.IdStr).
			Msg("Failed to detect duplicate content, continuing with processing")
		// Continue processing even if duplicate detection fails
	}

	contentType, aiJudgment := s.aiCheckTweet(&tweet, listType)

	// Save the user
	user := &db.TwitterUser{
		UserID:          tweet.User.IdStr,
		ScreenName:      tweet.User.ScreenName,
		Name:            tweet.User.Name,
		FollowersCount:  tweet.User.FollowersCount,
		IsVerified:      tweet.User.Verified,
		ProfileImageURL: tweet.User.ProfileImageUrlHttps,
	}

	dbStart := time.Now()
	if err = s.db.SaveTwitterUser(user); err != nil {
		log.Error().
			Err(err).
			Str("user_id", user.UserID).
			Str("screen_name", user.ScreenName).
			Msg("Error saving user for list tweet")
		metrics.DatabaseOperationsTotal.WithLabelValues("save", "twitter_users", "error").Inc()
		return
	}
	metrics.DatabaseOperationsTotal.WithLabelValues("save", "twitter_users", "success").Inc()
	metrics.DatabaseOperationDuration.WithLabelValues("save", "twitter_users").Observe(time.Since(dbStart).Seconds())

	// Convert the tweet to a JSON object
	tweetJSON := make(db.JSONB)
	tweetBytes, err := json.Marshal(tweet)
	if err != nil {
		log.Error().
			Err(err).
			Str("tweet_id", tweet.IdStr).
			Msg("Error marshaling list tweet")
	} else {
		if err = json.Unmarshal(tweetBytes, &tweetJSON); err != nil {
			log.Error().
				Err(err).
				Str("tweet_id", tweet.IdStr).
				Msg("Error unmarshaling list tweet to JSONB")
		}
	}

	var sourceListType string
	if listType != "KOLs" {
		sourceListType = "Projects"
	} else {
		sourceListType = listType
	}
	log.Debug().Str("tweet_id", tweet.IdStr).Str("list_type", listType).Str("source_list_type", sourceListType).Msg("Determined source list type for tweet")

	// Save the tweet
	dbTweet := &db.Tweet{
		TweetID:               tweet.IdStr,
		UserIDFK:              tweet.User.IdStr,
		TextContent:           tweet.FullText,
		FullTweetJSON:         tweetJSON,
		PublishedAt:           tweet.TweetCreatedAt,
		ViewsCount:            tweet.ViewsCount,
		ReplyCount:            tweet.ReplyCount,
		RetweetCount:          tweet.RetweetCount,
		FavoriteCount:         tweet.FavoriteCount,
		BookmarkCount:         tweet.BookmarkCount,
		ContainsTargetKeyword: false,
		ContentType:           contentType,
		TweetType:             tweet.Type,
		SourceListType:        sourceListType,
		AIJudgment:            aiJudgment,
		BulletPoints:          tweet.BulletPoints,
		ArticleTitle:          tweet.ArticleTitle,
		ArticlePreviewText:    tweet.ArticlePreviewText,
		ArticleCoverURL:       tweet.ArticleCoverURL,
		FullArticleJSON:       tweet.ArticleJSON,
		// AI Category fields
		IsProductUpdate:        tweet.IsProductUpdate,
		IsBusinessData:         tweet.IsBusinessData,
		IsEcosystemPartnership: tweet.IsEcosystemPartnership,
		IsProfitOpportunity:    tweet.IsProfitOpportunity,
		IsIndustryEvent:        tweet.IsIndustryEvent,
		IsOthers:               tweet.IsOthers,
	}
	if existTweet != nil && existTweet.ID > 0 {
		dbTweet.BaseModel = existTweet.BaseModel
	}

	// Set up notification state for deferred Telegram notification
	// For "important" list type, enable notification if AI deems it important
	if listType == "important" && aiJudgment == "YES" {
		shouldSendTelegramNotification = true
		finalDBTweet = dbTweet
	}

	dbStart = time.Now()
	if err = s.db.SaveTweet(dbTweet); err != nil {
		log.Error().
			Err(err).
			Str("tweet_id", dbTweet.TweetID).
			Msg("Error saving list tweet")
		metrics.DatabaseOperationsTotal.WithLabelValues("save", "tweets", "error").Inc()
		return
	}
	metrics.DatabaseOperationsTotal.WithLabelValues("save", "tweets", "success").Inc()
	metrics.DatabaseOperationDuration.WithLabelValues("save", "tweets").Observe(time.Since(dbStart).Seconds())

	// 创建或获取标签，并关联到 Tweet
	tag, err := s.db.GetOrCreateTag(listType)
	if err != nil {
		log.Error().Err(err).Str("keyword", listType).Msg("Error creating tag")
		return
	}

	// 关联标签与推文
	if err := s.db.AssociateTweetWithTags(dbTweet.ID, []db.Tag{*tag}); err != nil {
		log.Error().Err(err).Str("tweet_id", dbTweet.TweetID).Str("tag", listType).Msg("Error associating tag with tweet")
		return
	}

	log.Debug().
		Str("tweet_id", tweet.IdStr).
		Str("user", tweet.User.ScreenName).
		Str("list_type", listType).
		Str("ai_judgment", aiJudgment).
		Dur("duration", time.Since(start)).
		Msg("Processed list tweet")
}

func (s *TwitterService) aiCheckTweet(tweet *Tweet, listType string) (string, string) {
	// Check if tweet is of type article
	var contentType = "tweet"
	var checkText = tweet.FullText
	if tweet.Type == "article" {
		contentType = "article"
	}
	for _, l := range tweet.Entities.URLs {
		if strings.Contains(l.ExpandedUrl, "x.com/i/article/") {
			contentType = "article"
			idStr := tweet.IdStr
			articleData, err := s.fetchArticleData(idStr)
			if err != nil {
				log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error fetching article data")
			} else {
				articleJSON, err := json.Marshal(articleData)
				if err != nil {
					log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error marshaling article data")
				} else {
					var jsonbData db.JSONB
					if err := json.Unmarshal(articleJSON, &jsonbData); err != nil {
						log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error unmarshaling article data to JSONB")
						tweet.ArticleJSON = db.JSONB{}
					} else {
						tweet.ArticleJSON = jsonbData
					}
				}
				if articleMap, ok := articleData["article"].(map[string]interface{}); ok {
					if title, ok := articleMap["title"].(string); ok {
						tweet.ArticleTitle = title
					}
					if previewText, ok := articleMap["preview_text"].(string); ok {
						tweet.ArticlePreviewText = previewText
						checkText = previewText
					}
					if coverURL, ok := articleMap["cover_url"].(string); ok {
						tweet.ArticleCoverURL = coverURL
					}
					if contentInterface, ok := articleMap["content_state"]; ok {
						// 将 contentInterface 转换为 JSON
						contentJSON, err := json.Marshal(contentInterface)
						if err != nil {
							log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error marshaling content_state to JSON")
						} else {
							// 解析为 ArticleContentState 结构体
							var articleContent ArticleContentState
							if err := json.Unmarshal(contentJSON, &articleContent); err != nil {
								log.Error().Err(err).Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error unmarshaling to ArticleContentState")
							} else {
								// 提取所有文本并组合
								var textParts []string
								for _, block := range articleContent.Blocks {
									textParts = append(textParts, block.Text)
								}

								articleText := strings.Join(textParts, " ")
								if len(articleText) > len(checkText) {
									checkText = articleText
									log.Debug().Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Str("article_text", articleText).Msg("Updated checkText with article content")
								}
							}
						}
					} else {
						log.Error().Str("tweet_id", tweet.IdStr).Str("article_id", idStr).Msg("Error getting content_state from article")
					}
				}
			}
			break
		}
	}

	// AI Judgment
	aiJudgment := "NO"
	var isJsonRequest = false
	if s.aiService != nil {
		switch listType {
		case "KOLs":
			if strings.Contains(checkText, "Daily Report") {
				aiJudgment = "YES"
			}

			if strings.Contains(checkText, "Daily Hights") {
				aiJudgment = "YES"
			}

			if strings.Contains(checkText, "Daily Highlights") {
				aiJudgment = "YES"
			}

			if contentType == "article" {
				aiJudgment = "YES"
			}

			log.Info().Str("tweet_id", tweet.IdStr).
				Str("list type", listType).
				Str("ai judgment request check text", checkText).
				Str("ai judgment response", aiJudgment).Msg("AI judgment for list tweet")
		default:
			var prompt string
			isJsonRequest = true
			prompt = fmt.Sprintf(tweetClassificationPromptTemplate, checkText)

			req := &ai.Request{
				SessionId:     "ai-agent-judgment-" + tweet.IdStr, // Use TweetID instead of IdStr for consistency if TweetID is the primary key
				Content:       prompt,
				IsJsonRequest: isJsonRequest,
			}

			response, err := s.aiService.ProcessRequest(context.Background(), req)
			if err != nil {
				log.Warn().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error getting AI judgment for list tweet")
			} else {
				cleanedResponse := strings.TrimSpace(response.Response)
				cleanedResponse = strings.TrimPrefix(cleanedResponse, "```json")
				cleanedResponse = strings.TrimSuffix(cleanedResponse, "```")
				cleanedResponse = strings.TrimSpace(cleanedResponse) // Trim again after removing backticks
				// Try to parse JSON response first
				var aiInfo ProjectTweetAiINfo
				if jsonErr := json.Unmarshal([]byte(cleanedResponse), &aiInfo); jsonErr == nil {
					// Successfully parsed JSON, extract categories
					if aiInfo.IsImportant {
						aiJudgment = "YES"
						// Set category flags for later use in dbTweet
						tweet.IsProductUpdate = aiInfo.Categories.IsProductUpdate
						tweet.IsBusinessData = aiInfo.Categories.IsBusinessData
						tweet.IsEcosystemPartnership = aiInfo.Categories.IsEcosystemPartnership
						tweet.IsProfitOpportunity = aiInfo.Categories.IsProfitOpportunity
						tweet.IsIndustryEvent = aiInfo.Categories.IsIndustryEvent

						if !tweet.IsProductUpdate &&
							!tweet.IsBusinessData &&
							!tweet.IsEcosystemPartnership &&
							!tweet.IsProfitOpportunity &&
							!tweet.IsIndustryEvent {
							tweet.IsOthers = true
						}
					}
				} else if strings.Contains(strings.ToUpper(response.Response), "YES") {
					// Fallback to old string-based detection
					aiJudgment = "YES"
					tweet.IsOthers = true
				} else if !strings.Contains(strings.ToUpper(response.Response), "NO") {
					log.Warn().Err(jsonErr).Str("tweet_id", tweet.IdStr).Msg("Error parsing AI judgment response for list tweet")
				}
			}
			if err == nil {
				log.Info().Str("tweet_id", tweet.IdStr).
					Str("list type", listType).
					Str("ai judgment request checktext", checkText).
					Str("ai judgment response", response.Response).Msg("AI judgment for list tweet")
			}
		}

	}

	if aiJudgment == "YES" && s.aiService != nil {
		bulletPointsPromptFormat := `请你扮演一个信息提取助手。你的任务是根据我提供的文本，按照以下要求提取关键信息：

1.  **提取模式**：
    * 一个概括性的标题 (title)。标题以【】包裹，概括事件核心内容，比如 【Bittensor v0.20.0 正式发布】。
    * 正文 (bullet points)，当前仅需要返回一条，描述事件背景、主要情况及影响，控制在100字以内。
	* 风格简洁正式，突出事实与数据

2.  **语言版本**：
    * 你需要为提取的标题和正文，分别提供英文 (english) 和中文 (chinese) 两个版本。

3.  **输出格式（严格遵守）**：
    请严格按照以下 JSON 结构返回结果，不要添加任何额外的解释或说明文字，只返回 JSON 对象：
    %s

    确保 %sbullet_points%s 是一个字符串数组（当时只会有且仅有一个元素）。

请处理以下文本：%s`

		jsonStructure := "```json\n{\n  \"english\": {\n    \"title\": \"YOUR_ENGLISH_TITLE_HERE\",\n    \"bullet_points\": [\n      \"English bullet point 1.\",\n      \"English bullet point 2.\",\n      \"...\"\n    ]\n  },\n  \"chinese\": {\n    \"title\": \"你的中文标题在这里\",\n    \"bullet_points\": [\n      \"中文要点1。\",\n      \"中文要点2。\",\n      \"...\"\n    ]\n  }\n}\n```"

		// Use backticks for bullet_points in the prompt to avoid markdown interpretation issues if any
		finalBulletPointsPrompt := fmt.Sprintf(bulletPointsPromptFormat, jsonStructure, "`", "`", checkText)

		aiBulletPointsReq := &ai.Request{
			SessionId:     "ai-bullet-points-" + tweet.IdStr,
			Content:       finalBulletPointsPrompt,
			IsJsonRequest: true,
		}

		aiBulletPointsResponse, err := s.aiService.ProcessRequest(context.Background(), aiBulletPointsReq)
		if err != nil {
			log.Warn().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error getting AI bullet points for tweet")
		} else {
			log.Info().Str("tweet_id", tweet.IdStr).
				Str("list type", listType).
				Str("ai bullet points request checktext", checkText).
				Str("ai bullet points response", aiBulletPointsResponse.Response).Msg("AI bullet points for list tweet")
			// Attempt to clean the response to get only the JSON part
			// AI might sometimes add "```json" and "```" around the JSON block.
			cleanedResponse := strings.TrimSpace(aiBulletPointsResponse.Response)
			cleanedResponse = strings.TrimSuffix(cleanedResponse, "```")
			cleanedResponse = strings.TrimSpace(cleanedResponse) // Trim again after removing backticks

			var jsonbData db.JSONB
			if err := json.Unmarshal([]byte(cleanedResponse), &jsonbData); err != nil {
				log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Error unmarshaling bullet points to JSONB")
				tweet.BulletPoints = db.JSONB{}
			} else {
				tweet.BulletPoints = jsonbData
			}
		}
	}
	return contentType, aiJudgment
}

// fetchArticleData fetches article data from the SocialData API
func (s *TwitterService) fetchArticleData(articleID string) (map[string]interface{}, error) {
	apiURL := fmt.Sprintf("%s/twitter/article/%s", s.Config.BaseURL, articleID)
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request for article data: %v", err)
	}

	req.Header.Add("Authorization", "Bearer "+s.Config.APIKey)

	// Wait for rate limiter
	s.rateLimiter.Wait()

	// Record API request metrics
	apiStart := time.Now()
	resp, err := s.httpClient.Do(req)
	if err != nil {
		metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/article", "error").Inc()
		return nil, fmt.Errorf("error fetching article data: %v", err)
	}
	defer resp.Body.Close()

	// Record API request duration
	apiDuration := time.Since(apiStart)
	metrics.ExternalAPIRequestDuration.WithLabelValues("socialdata", "twitter/article").Observe(apiDuration.Seconds())

	// Check for rate limiting
	if resp.StatusCode == http.StatusTooManyRequests {
		retryAfter := resp.Header.Get("Retry-After")
		if retryAfter != "" {
			var seconds time.Duration
			seconds, err = time.ParseDuration(retryAfter + "s")
			if err == nil {
				log.Warn().
					Dur("retry_after", seconds).
					Msg("Rate limited by SocialData API for article data")
				time.Sleep(seconds)
			}
		}
		metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/article", "rate_limited").Inc()
		return nil, fmt.Errorf("rate limited by SocialData API")
	}

	// Check for other errors
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/article", "error").Inc()
		return nil, fmt.Errorf("error response from SocialData API: status code %d, response: %s", resp.StatusCode, string(body))
	}

	// Record successful API request
	metrics.ExternalAPIRequestsTotal.WithLabelValues("socialdata", "twitter/article", "success").Inc()

	// Parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body for article data: %v", err)
	}

	var articleData map[string]interface{}
	if err := json.Unmarshal(body, &articleData); err != nil {
		return nil, fmt.Errorf("error parsing response for article data: %v", err)
	}

	// Check if a data key exists
	if data, ok := articleData["data"]; ok {
		return data.(map[string]interface{}), nil
	}

	return articleData, nil
}

func (s *TwitterService) tryToGetSolanaAddress(address string) string {
	_, err := solana.PublicKeyFromBase58(address)
	if err == nil {
		return address
	}
	resp, err := s.caService.tokenService.fetchDexScreenerPairData(address, "solana")
	if err != nil {
		log.Warn().
			Err(err).
			Str("ca_address", address).
			Msg("Failed to fetch Solana token data from DexScreener")
		return ""
	}
	return resp.BaseToken.Address
}

// sendTelegramNotification sends a Telegram notification for a tweet
// This method can be called synchronously or asynchronously (with go keyword)
func (s *TwitterService) sendTelegramNotification(tweet Tweet, dbTweet db.Tweet) {
	// Check if notification has already been sent to prevent duplicates
	notificationSent, err := s.db.CheckTelegramNotificationStatus(dbTweet.TweetID)
	if err != nil {
		log.Error().Err(err).Str("tweet_id", dbTweet.TweetID).Msg("Error checking Telegram notification status")
		return
	}

	if notificationSent {
		log.Debug().Str("tweet_id", dbTweet.TweetID).Msg("Telegram notification already sent for this tweet, skipping")
		metrics.TelegramNotificationsTotal.WithLabelValues("skipped_duplicate").Inc()
		metrics.TelegramNotificationDuplicatesSkipped.Inc()
		return
	}

	tweetURL := fmt.Sprintf("https://x.com/%s/status/%s", tweet.User.ScreenName, tweet.IdStr)

	// Extract title and bullet points from BulletPoints JSON
	var title string
	var bulletPoints []string

	if dbTweet.BulletPoints != nil {
		// Try to extract English content from BulletPoints
		if englishData, ok := dbTweet.BulletPoints["english"]; ok {
			if englishMap, ok := englishData.(map[string]interface{}); ok {
				// Extract title
				if titleData, ok := englishMap["title"]; ok {
					if titleStr, ok := titleData.(string); ok {
						title = titleStr
					}
				}

				// Extract bullet points
				if bulletPointsData, ok := englishMap["bullet_points"]; ok {
					if bulletPointsArray, ok := bulletPointsData.([]interface{}); ok {
						for _, bp := range bulletPointsArray {
							if bpStr, ok := bp.(string); ok {
								bulletPoints = append(bulletPoints, bpStr)
							}
						}
					}
				}
			}
		}
	}

	// Fallback to article title or generate from content if no BulletPoints
	if title == "" {
		if tweet.ArticleTitle != "" {
			title = tweet.ArticleTitle
		} else {
			// Generate a simple title from the first part of the tweet
			words := strings.Fields(tweet.FullText)
			if len(words) > 8 {
				title = strings.Join(words[:8], " ") + "..."
			} else {
				title = tweet.FullText
			}
		}
	}

	// Format bullet points with "·" prefix
	var bulletPointsText string
	if len(bulletPoints) > 0 {
		var formattedBulletPoints []string
		for _, bp := range bulletPoints {
			formattedBulletPoints = append(formattedBulletPoints, "· "+bp)
		}
		bulletPointsText = strings.Join(formattedBulletPoints, "\n")
	} else {
		// Fallback to tweet content if no bullet points
		bulletPointsText = "· " + tweet.FullText
	}

	// Create the main content (title + bullet points)
	mainContent := "*" + title + "*" + "\n\n" + bulletPointsText

	// fixme: 14000 Check if content exceeds 140 characters for "More" functionality
	const maxVisibleChars = 14000

	if len(mainContent) > maxVisibleChars {
		// Find a good break point near 140 characters
		breakPoint := maxVisibleChars
		for breakPoint > 0 && mainContent[breakPoint] != ' ' && mainContent[breakPoint] != '\n' {
			breakPoint--
		}
		if breakPoint == 0 {
			breakPoint = maxVisibleChars
		}

		visibleContent := mainContent[:breakPoint]
		hiddenContent := mainContent[breakPoint:]

		// Use Telegram spoiler syntax for "More" content
		mainContent = visibleContent + "||" + hiddenContent + "||"
	}

	// Build the message according to the new format
	messageText := fmt.Sprintf(
		"*Project: %s @%s*\n\n%s\n\n🔗[Link](%s)",
		tweet.User.Name,
		tweet.User.ScreenName,
		mainContent,
		tweetURL,
	)

	err = s.telegramService.SendMessage(messageText)
	if err != nil {
		log.Error().Err(err).Str("tweet_id", tweet.IdStr).Msg("Failed to send Telegram notification")
		metrics.TelegramNotificationsTotal.WithLabelValues("failed").Inc()
		return
	}

	// Mark notification as sent in database after successful delivery
	if err := s.db.MarkTelegramNotificationSent(dbTweet.TweetID); err != nil {
		log.Error().Err(err).Str("tweet_id", dbTweet.TweetID).Msg("Failed to mark Telegram notification as sent in database")
		// Note: We don't return here because the notification was actually sent successfully
		// The flag update failure is a separate issue that shouldn't affect the notification delivery
	} else {
		log.Info().Str("tweet_id", tweet.IdStr).Msg("Telegram notification sent successfully and marked in database")
		metrics.TelegramNotificationsTotal.WithLabelValues("sent").Inc()
	}
}

// tweetClassificationPromptTemplate 是一个用于分类推文是否包含重要信息的LLM提示词模板。
// 它被设计为高度结构化，以确保AI模型能够稳定、准确地遵循指令。
// 模板包含：
// 1. 任务描述: 明确分类目标和五大重要信息类别
// 2. 判断标准: 提供YES/NO的明确判断依据
// 3. 正反案例: 通过具体案例展示判断逻辑
// 4. 输出格式: 严格规定JSON结构，便于程序解析
const tweetClassificationPromptTemplate = `
# 任务描述

请判断以下推文是否包含以下任意一类重要信息：

1. **产品/技术更新**: 新功能发布、版本迭代、技术突破。
2. **业务关键数据**: 用户增长、交易量等里程碑数据，或质押收益、代币经济模型等关键指标。
3. **生态合作**: 与交易所、机构、项目方等的合作公告或战略合作。
4. **收益机会**: 直接影响用户资产增长的机会，如质押、空投、挖矿机制等。
5. **行业大事件**: 如主网上线、通缩燃烧、治理投票等具有行业影响力的事件。

---

# 判断标准

- 若推文符合上述任意一类条件 → 回复 **YES**。
- 若推文仅为普通营销、社区活动、观点讨论等内容 → 回复 **NO**。

**判断原则**:
1. 严格按照判断标准进行分类，避免主观推测。
2. 若推文中同时包含多类信息，亦应返回 **YES**。
3. 优先识别具体的、可量化的信息，避免对模糊营销内容误判。

---

# 正面案例

## YES 案例
以下推文符合"产品/技术更新"和"收益机会"条件，应返回 YES：

> Genesis Update: Dedicated Claim Buttons for Genesis and Airdrop Allocations
> To improve clarity and convenience for our Virgens, we've updated the claim interface.
> Starting tomorrow, you'll see dedicated claim buttons for Genesis and Airdrop allocations (if eligible).
> For Virgens who accidentally sold their I.R.I.S. airdrop allocation, we're offering a one-time buyback window.
> Although the UI/UX distinguished between Genesis and airdrop allocations, the single unified claim button may have caused confusion.
> To preserve your airdrop eligibility, you must buy back at least the same amount of I.R.I.S. tokens you sold from your airdrop allocation before the snapshot deadline.
> Snapshot Deadline: June 14, 11:30 AM SGT / 03:30 AM UTC
> As always, we're working to make Genesis more intuitive, more fair, and more Virgen-friendly.
> Virgenity.

**分析**: 包含产品界面更新、空投机制说明、具体截止时间等重要信息。

## NO 案例
以下推文内容与产品进展、重大合作、重要事件、业务数据等无关，应返回 NO：

> It's just a little cell, what can it do?
> #AICell: rebuilds half the network overnight 🧠💥
> We build in bits. 🐝

**分析**: 纯营销话术，无具体信息或实质内容。

---

# 输出格式

**绝对禁止**输出任何解释、思考过程、道歉或额外的文字。
**唯一**的输出必须是一个结构如下的JSON对象：

{
  "is_important": boolean,
  "categories": {
    "is_product_update": boolean,
    "is_business_data": boolean,
    "is_ecosystem_partnership": boolean,
    "is_profit_opportunity": boolean,
    "is_industry_event": boolean
  }
}

---

现在，请基于以上所有规则，分析以下推文：
%s
`

type ArticleContentState struct {
	Blocks []struct {
		Text string `json:"text"`
	} `json:"blocks"`
}

// ReclassifyIsOthersTweets reprocesses all tweets with is_others = true by running AI classification again
// This method is designed to be called on startup if the configuration flag is enabled
func (s *TwitterService) ReclassifyIsOthersTweets() {
	log.Info().Msg("Starting AI re-classification of tweets with is_others = true")

	// Set default batch size if not configured
	batchSize := s.Config.AIReclassifyBatchSize
	if batchSize <= 0 {
		batchSize = 50 // Default batch size
	}

	offset := 0
	totalProcessed := 0
	totalUpdated := 0

	for {
		// Base query for tweets with is_others = true
		query := s.db.DB.Preload("User").Where("is_others = ?", true).Order("published_at desc")

		// If a re-classification timestamp is configured, only process tweets created after this time
		if s.Config.AIReclassifyTimestamp > 0 {
			parsedTime := time.Unix(s.Config.AIReclassifyTimestamp, 0)
			log.Info().Time("reclassify_after", parsedTime).Msg("Filtering tweets for re-classification by timestamp")
			query = query.Where("published_at > ?", parsedTime)
		}

		// Query tweets in batches
		var tweets []db.Tweet
		err := query.Limit(batchSize).Offset(offset).Find(&tweets).Error

		if err != nil {
			log.Error().Err(err).Msg("Failed to query tweets with is_others = true")
			return
		}

		// If no more tweets, break the loop
		if len(tweets) == 0 {
			break
		}

		log.Info().Int("batch_size", len(tweets)).Int("offset", offset).Msg("Processing batch of tweets for re-classification")

		// Process each tweet in the batch
		for _, dbTweet := range tweets {
			// Convert db.Tweet to Tweet struct for AI processing
			var tweet Tweet

			// If FullTweetJSON exists, try to unmarshal it to get more complete tweet data
			if dbTweet.FullTweetJSON != nil {
				// Convert JSONB (map[string]interface{}) to JSON bytes first
				if jsonBytes, err := json.Marshal(dbTweet.FullTweetJSON); err == nil {
					// Then unmarshal to Tweet struct to get complete data
					if err := json.Unmarshal(jsonBytes, &tweet); err != nil {
						log.Debug().Err(err).Str("tweet_id", dbTweet.TweetID).Msg("Failed to unmarshal FullTweetJSON, using basic tweet data")
					}
				} else {
					log.Debug().Err(err).Str("tweet_id", dbTweet.TweetID).Msg("Failed to marshal FullTweetJSON to bytes")
				}
			}

			// Run AI classification
			_, aiJudgment := s.aiCheckTweet(&tweet, dbTweet.SourceListType)

			// Only update if AI judgment is YES and classification flags have changed
			if aiJudgment == "YES" {
				// Check if any classification flags have changed
				hasChanges := dbTweet.IsProductUpdate != tweet.IsProductUpdate ||
					dbTweet.IsBusinessData != tweet.IsBusinessData ||
					dbTweet.IsEcosystemPartnership != tweet.IsEcosystemPartnership ||
					dbTweet.IsProfitOpportunity != tweet.IsProfitOpportunity ||
					dbTweet.IsIndustryEvent != tweet.IsIndustryEvent ||
					dbTweet.IsOthers != tweet.IsOthers

				if hasChanges {
					// Update the tweet with new classification
					s.updateTweetAndUser(aiJudgment, tweet, "")
					totalUpdated++

					log.Info().Str("tweet_id", dbTweet.TweetID).
						Bool("old_is_others", dbTweet.IsOthers).
						Bool("new_is_others", tweet.IsOthers).
						Bool("new_is_product_update", tweet.IsProductUpdate).
						Bool("new_is_business_data", tweet.IsBusinessData).
						Bool("new_is_ecosystem_partnership", tweet.IsEcosystemPartnership).
						Bool("new_is_profit_opportunity", tweet.IsProfitOpportunity).
						Bool("new_is_industry_event", tweet.IsIndustryEvent).
						Msg("Updated tweet classification")
				} else {
					log.Debug().Str("tweet_id", dbTweet.TweetID).Msg("No classification changes detected")
				}
			} else {
				// AI judgment is NO, but tweet was previously classified as is_others = true
				// This might indicate the AI model has changed or improved
				log.Debug().Str("tweet_id", dbTweet.TweetID).
					Str("ai_judgment", aiJudgment).
					Msg("AI judgment changed from YES to NO for previously classified tweet")
			}

			totalProcessed++

			// Add a small delay to avoid overwhelming the AI service
			time.Sleep(100 * time.Millisecond)
		}

		offset += batchSize

		// Log progress every 10 batches
		if (offset/batchSize)%10 == 0 {
			log.Info().Int("total_processed", totalProcessed).Int("total_updated", totalUpdated).Msg("Re-classification progress")
		}
	}

	log.Info().Int("total_processed", totalProcessed).Int("total_updated", totalUpdated).Msg("Completed AI re-classification of tweets with is_others = true")
}
