package services

import (
	"context"
	"testing"
	"time"

	"real-time-ca-service/internal/config"

	"github.com/alicebob/miniredis/v2"
	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestRedis(t *testing.T) (*RedisService, func()) {
	// Start a mini Redis server for testing
	mr, err := miniredis.Run()
	require.NoError(t, err)

	// Create Redis configuration
	cfg := config.RedisConfig{
		Host:                mr.Host(),
		Port:                6379, // miniredis uses a random port, but we'll override with Addr
		DB:                  0,
		PoolSize:            5,
		MinIdleConns:        1,
		MaxRetries:          3,
		DialTimeout:         5 * time.Second,
		ReadTimeout:         3 * time.Second,
		WriteTimeout:        3 * time.Second,
		PoolTimeout:         4 * time.Second,
		IdleTimeout:         5 * time.Minute,
		IdleCheckFrequency:  1 * time.Minute,
		LockTimeout:         10 * time.Second,
		LockRetryDelay:      100 * time.Millisecond,
		LockMaxRetries:      5,
		LockRefreshInterval: 2 * time.Second,
	}

	// Create Redis client with miniredis address
	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
		DB:   0,
	})

	// Create Redis service manually for testing
	redisService := &RedisService{
		client: client,
		config: cfg,
	}

	// Initialize the locker
	redisService.locker = redislock.New(client)

	cleanup := func() {
		client.Close()
		mr.Close()
	}

	return redisService, cleanup
}

func TestDistributedLockManager_AcquireLock(t *testing.T) {
	redisService, cleanup := setupTestRedis(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	ctx := context.Background()

	t.Run("successful lock acquisition", func(t *testing.T) {
		lockKey := "test_lock_1"

		lock, err := lockManager.AcquireLock(ctx, lockKey, nil)
		require.NoError(t, err)
		require.NotNil(t, lock)

		assert.Equal(t, lockKey, lock.GetKey())

		// Clean up
		err = lock.ReleaseLock()
		assert.NoError(t, err)
	})

	t.Run("lock acquisition with custom options", func(t *testing.T) {
		lockKey := "test_lock_2"
		opts := &LockOptions{
			Timeout:    5 * time.Second,
			RetryDelay: 50 * time.Millisecond,
			MaxRetries: 3,
			Metadata:   "test_metadata",
		}

		lock, err := lockManager.AcquireLock(ctx, lockKey, opts)
		require.NoError(t, err)
		require.NotNil(t, lock)

		assert.Equal(t, lockKey, lock.GetKey())

		// Clean up
		err = lock.ReleaseLock()
		assert.NoError(t, err)
	})

	t.Run("concurrent lock acquisition should fail", func(t *testing.T) {
		lockKey := "test_lock_3"

		// Acquire first lock
		lock1, err := lockManager.AcquireLock(ctx, lockKey, nil)
		require.NoError(t, err)
		require.NotNil(t, lock1)

		// Try to acquire the same lock - should fail
		opts := &LockOptions{
			Timeout:    1 * time.Second,
			RetryDelay: 100 * time.Millisecond,
			MaxRetries: 2,
		}

		lock2, err := lockManager.AcquireLock(ctx, lockKey, opts)
		assert.Error(t, err)
		assert.Nil(t, lock2)

		// Clean up
		err = lock1.ReleaseLock()
		assert.NoError(t, err)
	})
}

func TestDistributedLock_ReleaseLock(t *testing.T) {
	redisService, cleanup := setupTestRedis(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	ctx := context.Background()

	t.Run("successful lock release", func(t *testing.T) {
		lockKey := "test_release_1"

		lock, err := lockManager.AcquireLock(ctx, lockKey, nil)
		require.NoError(t, err)
		require.NotNil(t, lock)

		err = lock.ReleaseLock()
		assert.NoError(t, err)

		// Should be able to acquire the same lock again
		lock2, err := lockManager.AcquireLock(ctx, lockKey, nil)
		require.NoError(t, err)
		require.NotNil(t, lock2)

		err = lock2.ReleaseLock()
		assert.NoError(t, err)
	})

	t.Run("double release should not error", func(t *testing.T) {
		lockKey := "test_release_2"

		lock, err := lockManager.AcquireLock(ctx, lockKey, nil)
		require.NoError(t, err)
		require.NotNil(t, lock)

		err = lock.ReleaseLock()
		assert.NoError(t, err)

		// Second release should not error
		err = lock.ReleaseLock()
		assert.NoError(t, err)
	})
}

func TestDistributedLock_GetTTL(t *testing.T) {
	redisService, cleanup := setupTestRedis(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)
	ctx := context.Background()

	t.Run("get TTL of active lock", func(t *testing.T) {
		lockKey := "test_ttl_1"
		opts := &LockOptions{
			Timeout: 5 * time.Second,
		}

		lock, err := lockManager.AcquireLock(ctx, lockKey, opts)
		require.NoError(t, err)
		require.NotNil(t, lock)

		ttl, err := lock.GetTTL()
		assert.NoError(t, err)
		assert.Greater(t, ttl, time.Duration(0))
		assert.LessOrEqual(t, ttl, 5*time.Second)

		err = lock.ReleaseLock()
		assert.NoError(t, err)
	})
}

func TestDistributedLockManager_DefaultValues(t *testing.T) {
	redisService, cleanup := setupTestRedis(t)
	defer cleanup()

	lockManager := NewDistributedLockManager(redisService)

	assert.Equal(t, redisService.config.LockTimeout, lockManager.defaultTimeout)
	assert.Equal(t, redisService.config.LockRetryDelay, lockManager.defaultRetryDelay)
	assert.Equal(t, redisService.config.LockMaxRetries, lockManager.defaultMaxRetries)
	assert.Equal(t, redisService.config.LockRefreshInterval, lockManager.refreshInterval)
}
