package ai

import (
	"strings"
	"testing"
)

func TestExtractJSON(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "<PERSON> JSON",
			input:    `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]}`,
			expected: `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]}`,
		},
		{
			name:     "<PERSON><PERSON><PERSON> with text before",
			input:    `Here is the result: {"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]}`,
			expected: `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]}`,
		},
		{
			name:     "<PERSON><PERSON><PERSON> with text after",
			input:    `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]} I hope this helps!`,
			expected: `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]}`,
		},
		{
			name:     "JSON with text before and after",
			input:    `Here is the result: {"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]} I hope this helps!`,
			expected: `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]}`,
		},
		{
			name:     "No JSON",
			input:    `There are no contract addresses in this text.`,
			expected: ``,
		},
		{
			name:     "Empty string",
			input:    ``,
			expected: ``,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := extractJSON(tc.input)
			if result != tc.expected {
				t.Errorf("Expected %q, got %q", tc.expected, result)
			}
		})
	}
}

func TestParseContractDetectionResponse(t *testing.T) {
	testCases := []struct {
		name           string
		input          string
		expectedCount  int
		expectedError  bool
		expectedFilter bool // true if we expect filtering of low confidence results
	}{
		{
			name:          "Valid response with one address",
			input:         `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95}]}`,
			expectedCount: 1,
			expectedError: false,
		},
		{
			name:          "Valid response with multiple addresses",
			input:         `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95},{"address":"9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b","chain_type":"solana","confidence":0.9}]}`,
			expectedCount: 2,
			expectedError: false,
		},
		{
			name:          "Empty addresses array",
			input:         `{"addresses":[]}`,
			expectedCount: 0,
			expectedError: false,
		},
		{
			name:          "Invalid JSON",
			input:         `{"addresses":`,
			expectedCount: 0,
			expectedError: true,
		},
		{
			name:           "Low confidence address",
			input:          `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.4}]}`,
			expectedCount:  0,
			expectedError:  false,
			expectedFilter: true,
		},
		{
			name:           "Mixed confidence addresses",
			input:          `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.95},{"address":"0xabcdef1234567890abcdef1234567890abcdef12","chain_type":"evm","confidence":0.4}]}`,
			expectedCount:  1,
			expectedError:  false,
			expectedFilter: true,
		},
		{
			name:           "Moderate confidence address",
			input:          `{"addresses":[{"address":"0x1234567890abcdef1234567890abcdef12345678","chain_type":"evm","confidence":0.6}]}`,
			expectedCount:  1,
			expectedError:  false,
			expectedFilter: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			addresses, err := parseContractDetectionResponse(tc.input)

			// Check error
			if tc.expectedError && err == nil {
				t.Errorf("Expected error, got nil")
			}
			if !tc.expectedError && err != nil {
				t.Errorf("Expected no error, got %v", err)
			}

			// If we expect an error, don't check the addresses
			if tc.expectedError {
				return
			}

			// Check address count
			if len(addresses) != tc.expectedCount {
				t.Errorf("Expected %d addresses, got %d", tc.expectedCount, len(addresses))
			}

			// Check filtering
			if tc.expectedFilter {
				// Verify all returned addresses have confidence >= 0.5
				for _, addr := range addresses {
					if addr.Confidence < 0.5 {
						t.Errorf("Expected all addresses to have confidence >= 0.5, got %f", addr.Confidence)
					}
				}
			}
		})
	}
}

// TestCreateContractDetectionPrompt tests the prompt creation function
func TestCreateContractDetectionPrompt(t *testing.T) {
	tweetText := "Check out this new token: 0x1234567890abcdef1234567890abcdef12345678"

	prompt := createContractDetectionPrompt(tweetText)

	// Check that the prompt contains the tweet text
	if !strings.Contains(prompt, tweetText) {
		t.Errorf("Prompt does not contain tweet text")
	}

	// Check that the prompt asks for JSON format
	if !strings.Contains(prompt, "JSON format") {
		t.Errorf("Prompt does not mention JSON format")
	}
}
