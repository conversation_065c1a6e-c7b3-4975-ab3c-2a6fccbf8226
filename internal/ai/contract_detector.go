package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
)

// ContractDetectionRequest represents a request to detect contract addresses
type ContractDetectionRequest struct {
	TweetID   string
	TweetText string
	// TweetAuthor string
}

// ContractAddress represents a detected contract address
type ContractAddress struct {
	Address    string  `json:"address"`
	ChainType  string  `json:"chain_type"`
	Confidence float64 `json:"confidence"`
}

// ContractDetectionResponse represents the response from contract address detection
type ContractDetectionResponse struct {
	Addresses []ContractAddress `json:"addresses"`
}

// DetectContractAddresses uses AI to detect contract addresses in a tweet
func (s *Service) DetectContractAddresses(ctx context.Context, req *ContractDetectionRequest) ([]ContractAddress, error) {
	if !s.config.Enabled {
		log.Debug().Msg("AI contract detection is disabled")
		return nil, nil
	}

	if req.TweetText == "" {
		return nil, fmt.Errorf("tweet text cannot be empty")
	}

	// Create a session ID based on tweet ID
	sessionID := fmt.Sprintf("contract_detection_%s", req.TweetID)

	// Create prompt for the AI
	prompt := createContractDetectionPrompt(req.TweetText)

	// Create AI request
	aiReq := &Request{
		SessionId: sessionID,
		Content:   prompt,
	}

	// Process request with retry logic
	var aiResp *Response
	var err error

	for i := 0; i <= s.config.MaxRetry; i++ {
		if i > 0 {
			log.Info().Int("attempt", i+1).Msg("Retrying AI contract detection")
			time.Sleep(time.Duration(i) * time.Second) // Exponential backoff
		}

		aiResp, err = s.ProcessRequest(ctx, aiReq)
		if err == nil {
			break
		}

		log.Warn().Err(err).Int("attempt", i+1).Msg("AI contract detection failed")
	}

	if err != nil {
		return nil, fmt.Errorf("all AI contract detection attempts failed: %v", err)
	}

	if aiResp == nil {
		return nil, fmt.Errorf("no AI response received")
	}

	// Parse the response
	addresses, err := parseContractDetectionResponse(aiResp.Response)
	if err != nil {
		log.Warn().Err(err).Str("response", aiResp.Response).Msg("Failed to parse AI response")
		return nil, fmt.Errorf("failed to parse AI response: %v", err)
	}

	log.Info().
		Str("tweet_id", req.TweetID).
		Int("address_count", len(addresses)).
		Msg("AI detected contract addresses")

	return addresses, nil
}

func createContractDetectionPrompt(tweetText string) string {
	return fmt.Sprintf(`You are a specialized AI for detecting blockchain contract addresses in tweets.
Analyze the following tweet and extract any potential contract addresses (CA). A contract address can be any string that appears in the context of "ca:", "contract address:", or similar indicators, or any long alphanumeric string that could plausibly be a blockchain address.

Tweet:
"%s"

For each contract address you find, determine which blockchain it belongs to (e.g., evm, solana). Consider the context and format of the address to make an educated guess if necessary.
Even if you're not entirely sure, include addresses with moderate confidence.

Respond in JSON format with the following structure:
{
  "addresses": [
    {
      "address": "the contract address",
      "chain_type": "evm or solana or other chain type",
      "confidence": 0.6 // a number between 0 and 1 indicating your confidence
    }
  ]
}

If no contract addresses are found, return an empty array for "addresses".
`, tweetText)
}

// parseContractDetectionResponse parses the AI response into a structured format
func parseContractDetectionResponse(response string) ([]ContractAddress, error) {
	// Extract JSON from the response (in case the AI included extra text)
	jsonStr := extractJSON(response)
	if jsonStr == "" {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	// Parse the JSON
	var result ContractDetectionResponse
	if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Filter out low confidence results
	var filteredAddresses []ContractAddress
	for _, addr := range result.Addresses {
		if addr.Confidence >= 0.5 && addr.Address != "" && addr.ChainType != "" {
			log.Debug().Str("filteredAddress", addr.Address).Float64("confidence", addr.Confidence).Msg("AI detected contract address")
			filteredAddresses = append(filteredAddresses, addr)
		}
	}

	return filteredAddresses, nil
}

// extractJSON extracts JSON from a string that might contain additional text
func extractJSON(text string) string {
	// Find the first opening brace
	start := strings.Index(text, "{")
	if start == -1 {
		return ""
	}

	// Find the last closing brace
	end := strings.LastIndex(text, "}")
	if end == -1 {
		return ""
	}

	// Extract the JSON
	return text[start : end+1]
}
