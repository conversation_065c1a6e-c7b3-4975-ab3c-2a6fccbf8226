package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/sashabaranov/go-openai"
)

// ChatMessage represents a message in the chat
type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatHistory represents the history of a conversation
type ChatHistory struct {
	Messages []ChatMessage `json:"messages"`
}

// Key prefix for chat history
const chatHistoryKeyPrefix = "chat:history:"

// Cache expiration time (one day)
const chatHistoryExpireSeconds = 86400 // 24 hours = 86400 seconds

// InMemoryCache is a simple in-memory cache implementation
type InMemoryCache struct {
	data  map[string]string
	mutex sync.RWMutex
}

// NewInMemoryCache creates a new in-memory cache
func NewInMemoryCache() *InMemoryCache {
	return &InMemoryCache{
		data: make(map[string]string),
	}
}

// Set adds or updates a key in the cache
func (c *InMemoryCache) Set(key, value string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.data[key] = value
}

// Get retrieves a value from the cache
func (c *InMemoryCache) Get(key string) (string, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	value, exists := c.data[key]
	return value, exists
}

// Delete removes a key from the cache
func (c *InMemoryCache) Delete(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	delete(c.data, key)
}

// Config holds configuration for the AI service
type Config struct {
	APIKey         string
	Model          string
	BaseURL        string
	Enabled        bool
	MaxRetry       int
	RequestsPerSec int // Rate limit for API requests per minute
}

// RateLimiter implements a token bucket rate limiter
type RateLimiter struct {
	rate          int           // Number of tokens per interval
	interval      time.Duration // Interval for token replenishment
	tokens        int           // Current number of tokens
	lastReplenish time.Time     // Last time tokens were replenished
	mu            sync.Mutex    // Mutex for thread safety
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(rate int, interval time.Duration) *RateLimiter {
	return &RateLimiter{
		rate:          rate,
		interval:      interval,
		tokens:        rate,
		lastReplenish: time.Now(),
	}
}

// Wait blocks until a token is available
func (r *RateLimiter) Wait() {
	log.Debug().Int("available_tokens", r.tokens).Msg("Rate limiter: waiting for token")
	r.mu.Lock()
	defer r.mu.Unlock()

	// Replenish tokens if needed
	r.replenishTokens()

	// If no tokens available, wait until next replenishment
	for r.tokens <= 0 {
		log.Debug().Msg("Rate limiter: no tokens available, waiting")
		r.mu.Unlock()
		time.Sleep(10 * time.Millisecond)
		r.mu.Lock()
		r.replenishTokens()
	}

	// Take a token
	r.tokens--
	log.Debug().Int("remaining_tokens", r.tokens).Msg("Rate limiter: token consumed")
}

// replenishTokens replenishes tokens based on elapsed time
func (r *RateLimiter) replenishTokens() {
	now := time.Now()
	elapsed := now.Sub(r.lastReplenish)

	// Calculate how many tokens to add
	tokensToAdd := int(float64(r.rate) * elapsed.Seconds() / r.interval.Seconds())
	if tokensToAdd > 0 {
		r.tokens = min(r.rate, r.tokens+tokensToAdd)
		r.lastReplenish = now
	}
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Service handles AI-related operations
type Service struct {
	cache       *InMemoryCache
	config      Config
	client      *openai.Client
	rateLimiter *RateLimiter
}

// NewService creates a new AI service
func NewService(config Config) *Service {
	// Create OpenAI client configuration
	clientConfig := openai.DefaultConfig(config.APIKey)

	// Set custom base URL if provided
	if config.BaseURL != "" {
		clientConfig.BaseURL = config.BaseURL
	}

	// Create client with configuration
	client := openai.NewClientWithConfig(clientConfig)

	// Ensure a minimum rate limit if not set or set too low
	rate := config.RequestsPerSec
	if rate <= 0 {
		rate = 8 // Default to 8 requests per minute if not set
		log.Warn().Int("default_rate", rate).Msg("AI rate limit not set or invalid, using default")
	}

	return &Service{
		cache:       NewInMemoryCache(),
		config:      config,
		client:      client,
		rateLimiter: NewRateLimiter(rate, time.Second), // Use rate from config or default
	}
}

// Request represents an AI request
type Request struct {
	SessionId     string
	Content       string
	IsJsonRequest bool
}

// Response represents an AI response
type Response struct {
	Response string
}

// ProcessRequest handles AI request processing
func (s *Service) ProcessRequest(ctx context.Context, req *Request) (*Response, error) {
	// Parameter validation
	if req.SessionId == "" {
		return nil, fmt.Errorf("session ID cannot be empty")
	}

	if req.Content == "" {
		return nil, fmt.Errorf("content cannot be empty")
	}

	// Generate cache key
	chatHistoryKey := fmt.Sprintf("%s%s", chatHistoryKeyPrefix, req.SessionId)

	// Get chat history from cache
	var chatHistory ChatHistory
	cachedHistory, exists := s.cache.Get(chatHistoryKey)
	if !exists {
		// Create new chat history if none exists
		chatHistory = ChatHistory{Messages: []ChatMessage{}}
	} else {
		// Parse existing chat history
		if err := json.Unmarshal([]byte(cachedHistory), &chatHistory); err != nil {
			// Create new chat history if parsing fails
			log.Warn().Err(err).Msg("Failed to unmarshal chat history")
			chatHistory = ChatHistory{Messages: []ChatMessage{}}
		}
	}

	// Add user's new input to chat history
	chatHistory.Messages = append(chatHistory.Messages, ChatMessage{
		Role:    "user",
		Content: req.Content,
	})

	// Convert chat history to OpenAI message format
	openaiMessages := make([]openai.ChatCompletionMessage, 0, len(chatHistory.Messages))
	for _, msg := range chatHistory.Messages {
		openaiMessages = append(openaiMessages, openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	// Create request and call OpenAI Completion API
	log.Debug().
		Str("session_id", req.SessionId).
		Int("message_count", len(openaiMessages)).
		Msg("Sending request to AI")

	// Build chat completion request parameters
	request := openai.ChatCompletionRequest{
		Messages:    openaiMessages,
		Model:       s.config.Model,
		Temperature: 0.7,
	}

	// Set JSON response format if requested
	if req.IsJsonRequest {
		log.Debug().Str("session_id", req.SessionId).Msg("Setting JSON response format")

		// Use standard JSON response format
		request.ResponseFormat = &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONObject,
		}
	}

	// Wait for rate limiter
	s.rateLimiter.Wait()

	// Start timing and send request
	startTime := time.Now()
	var chatCompletion openai.ChatCompletionResponse
	var err error

	for i := 0; i <= s.config.MaxRetry; i++ {
		if i > 0 {
			log.Info().Int("attempt", i+1).Str("session_id", req.SessionId).Msg("Retrying OpenAI API call")
			time.Sleep(time.Duration(i) * time.Second) // Exponential backoff
		}

		chatCompletion, err = s.client.CreateChatCompletion(ctx, request)
		if err == nil {
			break
		}

		log.Warn().Err(err).Int("attempt", i+1).Str("session_id", req.SessionId).Msg("OpenAI API call failed")
	}

	if err != nil {
		log.Error().Err(err).Str("session_id", req.SessionId).Msg("All OpenAI API call attempts failed")
		return nil, fmt.Errorf("AI call failed after %d retries: %v", s.config.MaxRetry+1, err)
	}

	// Log API call time
	log.Debug().
		Dur("duration", time.Since(startTime)).
		Str("session_id", req.SessionId).
		Msg("OpenAI API call complete")

	// Process response
	if len(chatCompletion.Choices) == 0 {
		return nil, fmt.Errorf("AI did not return a valid response")
	}

	aiResponse := chatCompletion.Choices[0].Message.Content

	// Add AI response to chat history
	chatHistory.Messages = append(chatHistory.Messages, ChatMessage{
		Role:    "assistant",
		Content: aiResponse,
	})

	// Save updated chat history to cache
	chatHistoryJson, err := json.Marshal(chatHistory)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to marshal chat history")
		// Continue to return results, don't affect user experience
	} else {
		// Save to cache
		s.cache.Set(chatHistoryKey, string(chatHistoryJson))
	}

	log.Debug().
		Str("session_id", req.SessionId).
		Int("message_count", len(chatHistory.Messages)).
		Msg("Completed AI request")

	// Return result
	res := &Response{
		Response: aiResponse,
	}
	return res, nil
}
