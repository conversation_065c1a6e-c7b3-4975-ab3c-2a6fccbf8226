package utils

import (
	"regexp"
	"testing"
)

func TestSolanaAddressRegex(t *testing.T) {
	solanaRegex := regexp.MustCompile(`[1-9A-HJ-NP-Za-km-z]{32,44}`)

	// Test address
	address := "4ttPqZqrm2cLwF2NeNMA2tPA341MQA2AybEBMGfJpump"

	// Check if it matches
	matches := solanaRegex.FindAllString(address, -1)

	t.Logf("Address: %s", address)
	t.Logf("Length: %d", len(address))
	t.Logf("Matches: %v", matches)
	t.Logf("Number of matches: %d", len(matches))

	if len(matches) == 0 {
		t.<PERSON><PERSON>("Expected to find matches for address %s, but found none", address)
	} else if len(matches) > 1 {
		t.<PERSON><PERSON><PERSON>("Expected to find 1 match for address %s, but found %d", address, len(matches))
	} else if matches[0] != address {
		t.<PERSON><PERSON>rf("Expected match '%s' to be equal to address '%s'", matches[0], address)
	}

	// If there is no match, analyze whether each character is in the allowed character set
	// The character analysis part is implicitly covered by the regex itself.
	// If the main regex fails, the detailed error above will be reported.
	// If specific character validation is still needed for non-matching cases (though the current test case should match):
	// if len(matches) == 0 { // This block would only run if the address is *expected* to be invalid
	// 	t.Log("\nCharacter Analysis (for non-matching address):")
	// 	for i, char := range address {
	// 		isValid := regexp.MustCompile(`[1-9A-HJ-NP-Za-km-z]`).MatchString(string(char))
	// 		t.Logf("Position %d: Char '%c' Valid: %v", i, char, isValid)
	// 		if !isValid {
	// 			t.Errorf("Character '%c' at position %d is invalid for Solana address", char, i)
	// 		}
	// 	}
	// }
}
