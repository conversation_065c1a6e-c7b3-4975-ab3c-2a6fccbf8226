package utils

import (
	"encoding/hex"
	"errors"
	"strings"

	"golang.org/x/crypto/sha3"
)

// ErrInvalidAddress 表示传入的字符串不是 20 字节的十六进制地址。
var ErrInvalidAddress = errors.New("invalid EVM address")

// ToChecksumAddress 将任意合法地址转成 EIP-55 Checksum 形式，带 0x 前缀。
func ToChecksumAddress(addr string) (string, error) {
	// 1. 去掉 0x/0X 前缀
	if strings.HasPrefix(addr, "0x") || strings.HasPrefix(addr, "0X") {
		addr = addr[2:]
	}
	// 2. 必须是 40 个十六进制字符（20 字节）
	if len(addr) != 40 {
		return "", ErrInvalidAddress
	}
	if _, err := hex.DecodeString(addr); err != nil { // 只要包含非十六进制字符就会报错
		return "", ErrInvalidAddress
	}

	lower := strings.ToLower(addr)

	// 3. 计算 Keccak-256 哈希（以太坊使用的 “Legacy” 版本）
	hasher := sha3.NewLegacyKeccak256()
	hasher.Write([]byte(lower))
	hash := hasher.Sum(nil) // 32 byte

	// 4. 按 EIP-55 规则混合大小写
	var chk [40]byte
	for i := 0; i < 40; i++ {
		ch := lower[i]
		if ch >= 'a' && ch <= 'f' { // 只有 a-f 需要大小写区分
			byteIdx := i / 2
			var nibble byte
			if i%2 == 0 {
				nibble = hash[byteIdx] >> 4 // 高 4 bit
			} else {
				nibble = hash[byteIdx] & 0x0f // 低 4 bit
			}
			if nibble >= 8 {
				ch -= 32 // 转成大写（ASCII 差 32）
			}
		}
		chk[i] = ch
	}

	return "0x" + string(chk[:]), nil
}
