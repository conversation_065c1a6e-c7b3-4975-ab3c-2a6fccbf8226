package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"real-time-ca-service/internal/db"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/services"
)

// Handler contains the API handlers
type Handler struct {
	twitterService *services.TwitterService
	caService      *services.CAService
	tokenService   *services.TokenService
	redisService   *services.RedisService
	scheduler      *cron.Cron
}

// NewHandler creates a new Handler
func NewHandler(twitterService *services.TwitterService, caService *services.CAService, tokenService *services.TokenService, redisService *services.RedisService) *Handler {
	return &Handler{
		twitterService: twitterService,
		caService:      caService,
		tokenService:   tokenService,
		redisService:   redisService,
		scheduler:      cron.New(),
	}
}

// TweetResponse represents a tweet response
type TweetResponse struct {
	TweetInfo
	ContractAddresses []CAResponse `json:"contract_addresses,omitempty"`
}

type TweetInfo struct {
	ID                 string         `json:"id"`
	Text               string         `json:"text"`
	PublishedAt        int64          `json:"published_at"`
	User               UserResponse   `json:"user"`
	Images             []string       `json:"images"`
	Tags               []string       `json:"tags,omitempty"`
	CollectionTags     []string       `json:"collection_tags,omitempty"`
	ReplyCount         int            `json:"reply_count"`
	RetweetCount       int            `json:"retweet_count"`
	FavoriteCount      int            `json:"favorite_count"`
	ViewsCount         int            `json:"views_count"`
	BookmarkCount      int            `json:"bookmark_count"`
	ContentType        string         `json:"content_type"`     // "tweet" or "article"
	SourceListType     string         `json:"source_list_type"` // "KOLs" or "Projects"
	ArticleTitle       string         `json:"article_title,omitempty"`
	ArticlePreviewText string         `json:"article_preview_text,omitempty"`
	ArticleCoverURL    string         `json:"article_cover_url,omitempty"`
	BulletPoints       interface{}    `json:"bullet_points,omitempty"`
	Notices            NoticeResponse `json:"notices,omitempty"`
}

type NoticeResponse struct {
	IsProductUpdate        bool `json:"is_product_update"`
	IsBusinessData         bool `json:"is_business_data"`
	IsEcosystemPartnership bool `json:"is_ecosystem_partnership"`
	IsProfitOpportunity    bool `json:"is_profit_opportunity"`
	IsIndustryEvent        bool `json:"is_industry_event"`
	IsOthers               bool `json:"is_others"`
}

// UserResponse represents a user response
type UserResponse struct {
	ID              string `json:"id"`
	Name            string `json:"name"`
	ScreenName      string `json:"screen_name"`
	ProfileImageURL string `json:"profile_image_url"`
	IsVerified      bool   `json:"is_verified"`
	FollowersCount  int    `json:"followers_count"`
}

// CAResponse represents a contract address response
type CAResponse struct {
	Address           string                  `json:"address"`
	ChainType         string                  `json:"chain_type"`
	IsRecognized      bool                    `json:"is_recognized"`
	TokenDetails      []*TokenDetailsResponse `json:"token_details,omitempty"` // Changed to array
	TradeTokenDetails []TradeTokenInfo        `json:"trade_token_details,omitempty"`
	Tweets            []TweetInfo             `json:"tweets,omitempty"`
	Tags              []string                `json:"tags,omitempty"`
}

// TokenDetailsResponse represents token details response
type TokenDetailsResponse struct {
	Name          string   `json:"name"`
	Symbol        string   `json:"symbol"`
	ChainID       string   `json:"chain_id"`
	Source        string   `json:"source"` // Added source field
	LogoURL       string   `json:"logo_url"`
	TwitterURL    string   `json:"twitter_url"`
	PairCreatedAt int64    `json:"pair_created_at"`
	HolderCount   *int64   `json:"holder_count"`
	MarketCapUSD  *float64 `json:"market_cap_usd"`
	PriceUSD      *float64 `json:"price_usd"`
}

// RecognizedCARequest represents a request to add a recognized CA
type RecognizedCARequest struct {
	CAAddress     string `json:"ca_address"`
	ChainID       string `json:"chain_id"`
	TokenNameHint string `json:"token_name_hint"`
}

// GetTweets godoc
// @Summary      Get tweets
// @Description  Retrieves a list of tweets
// @Tags         tweets
// @Accept       json
// @Produce      json
// @Param        limit   query     int     false  "Number of tweets to return (default: 20)"
// @Param        offset  query     int     false  "Offset for pagination (default: 0)"
// @Param        tags    query     []string  false  "Filter by tags (comma-separated, e.g. 'tag1,tag2')"
// @Param        collection_tags    query     []string  false  "Filter by collection tags (comma-separated, e.g. 'tag1,tag2')"
// @Success      200     {array}   TweetResponse
// @Failure      500     {object}  map[string]string
// @Router       /tweets [get]
func (h *Handler) GetTweets(c *gin.Context) {
	// Parse query parameters
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")
	tagsStr := c.Query("tags")

	limit := 20 // Default limit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // Default offset
	if offsetStr != "" {
		parsedOffset, err := strconv.Atoi(offsetStr)
		if err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	// Parse tags parameter (comma-separated)
	var tags []string
	if tagsStr != "" {
		tags = strings.Split(tagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range tags {
			tags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("tags", tags).Msg("Filtering tweets by tags")
	}

	if len(tags) == 0 {
		tags = []string{"ai agent ca"}
	}

	// Get tweets from the database with tag filtering
	tweets, err := h.twitterService.GetTweetsByTags(limit, offset, tags)
	if err != nil {
		log.Error().Err(err).Msg("Error getting tweets")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting tweets"})
		return
	}

	// Convert to response format
	response := make([]TweetResponse, 0, len(tweets))
	for _, tweet := range tweets {
		// Extract images from tweet JSON
		images := extractImagesFromTweet(tweet.FullTweetJSON)

		// Format contract addresses
		contractAddresses := make([]CAResponse, 0, len(tweet.ExtractedCAs))
		for _, ca := range tweet.ExtractedCAs {
			caResponse := CAResponse{
				Address:   ca.CAAddress,
				ChainType: ca.ChainType,
				// IsRecognized will be set based on token details
			}

			if len(ca.TokenDetails) > 0 {
				caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
				caResponse.IsRecognized = true

				// Process each token detail
				for _, tokenDetail := range ca.TokenDetails {
					tdResponse := &TokenDetailsResponse{
						Name:         tokenDetail.TokenName,
						Symbol:       tokenDetail.Symbol,
						ChainID:      tokenDetail.ChainID,
						Source:       tokenDetail.Source,
						LogoURL:      tokenDetail.TokenLogoURL,
						TwitterURL:   tokenDetail.TokenTwitterURL,
						HolderCount:  tokenDetail.HolderCount,
						MarketCapUSD: tokenDetail.MarketCapUSD,
						PriceUSD:     tokenDetail.PriceUSD,
					}

					// Format pair created at time
					if tokenDetail.PairCreatedAt != nil {
						tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
					}

					caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
				}
			}

			contractAddresses = append(contractAddresses, caResponse)
		}

		// Create tweet response
		tweetResponse := TweetResponse{
			TweetInfo: TweetInfo{
				ID:          tweet.TweetID,
				Text:        tweet.TextContent,
				PublishedAt: tweet.PublishedAt.Unix(),
				User: UserResponse{
					ID:              tweet.User.UserID,
					Name:            tweet.User.Name,
					ScreenName:      tweet.User.ScreenName,
					ProfileImageURL: tweet.User.ProfileImageURL,
					IsVerified:      tweet.User.IsVerified,
					FollowersCount:  tweet.User.FollowersCount,
				},
				Images: images,
			},
			ContractAddresses: contractAddresses,
		}

		// Add tweet tags if available
		if len(tweet.Tags) > 0 {
			tweetResponse.TweetInfo.Tags = make([]string, 0, len(tweet.Tags))
			for _, tag := range tweet.Tags {
				tweetResponse.TweetInfo.Tags = append(tweetResponse.TweetInfo.Tags, tag.Name)
			}
		}

		// Add collection tags if available
		if len(tweet.CollectionTags) > 0 {
			tweetResponse.TweetInfo.CollectionTags = tweet.CollectionTags
		}

		response = append(response, tweetResponse)
	}

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

func (h *Handler) AddRecognizedCA(c *gin.Context) {
	// Parse request body
	var req RecognizedCARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Validate request
	if req.CAAddress == "" || req.ChainID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "CA address and chain ID are required"})
		return
	}

	// Add recognized CA
	err := h.caService.AddRecognizedCA(req.CAAddress, req.ChainID, req.TokenNameHint)
	if err != nil {
		log.Error().Err(err).Msg("Error adding recognized CA")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error adding recognized CA"})
		return
	}

	// Return success
	c.JSON(http.StatusCreated, gin.H{"status": "success"})
}

// GetRecognizedCAs godoc
// @Summary      Get recognized contract addresses
// @Description  Retrieves a list of recognized contract addresses
// @Tags         contract-addresses
// @Accept       json
// @Produce      json
// @Param        limit   query     int     false  "Number of CAs to return (default: 20)"
// @Param        offset  query     int     false  "Offset for pagination (default: 0)"
// @Param        tags    query     []string  false  "Filter by tags (comma-separated, e.g. 'tag1,tag2')"
// @Success      200     {array}   CAResponse
// @Failure      500     {object}  map[string]string
// @Router       /recognized-cas [get]
func (h *Handler) GetRecognizedCAs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")
	tagsStr := c.Query("tags")

	limit := 20 // Default limit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // Default offset
	if offsetStr != "" {
		parsedOffset, err := strconv.Atoi(offsetStr)
		if err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	// Parse tags parameter (comma-separated)
	var tags []string
	if tagsStr != "" {
		tags = strings.Split(tagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range tags {
			tags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("tags", tags).Msg("Filtering CAs by tags")
	}

	if len(tags) == 0 {
		tags = []string{"ai agent ca"}
	}

	// Get recognized CAs from the database with tag filtering
	cas, err := h.caService.GetRecognizedCAsByTags(limit, offset, tags)
	if err != nil {
		log.Error().Err(err).Msg("Error getting recognized CAs")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting recognized CAs"})
		return
	}

	// Convert to response format
	response := make([]CAResponse, 0, len(cas))
	for _, ca := range cas {
		caResponse := CAResponse{
			Address:   ca.CAAddress,
			ChainType: ca.ChainType,
		}

		// Add CA tags
		if len(ca.Tags) > 0 {
			caResponse.Tags = make([]string, 0, len(ca.Tags))
			for _, tag := range ca.Tags {
				caResponse.Tags = append(caResponse.Tags, tag.Name)
			}
		}

		for _, tweet := range ca.Tweets {
			// Extract images from tweet JSON
			images := extractImagesFromTweet(tweet.FullTweetJSON)

			// Create tweet info
			tweetInfo := TweetInfo{
				ID:          tweet.TweetID,
				Text:        tweet.TextContent,
				PublishedAt: tweet.PublishedAt.Unix(),
				User: UserResponse{
					ID:              tweet.User.UserID,
					Name:            tweet.User.Name,
					ScreenName:      tweet.User.ScreenName,
					ProfileImageURL: tweet.User.ProfileImageURL,
					IsVerified:      tweet.User.IsVerified,
					FollowersCount:  tweet.User.FollowersCount,
				},
				Images: images,
			}

			// Add tweet tags if available
			if len(tweet.Tags) > 0 {
				tweetInfo.Tags = make([]string, 0, len(tweet.Tags))
				for _, tag := range tweet.Tags {
					tweetInfo.Tags = append(tweetInfo.Tags, tag.Name)
				}
			}

			// @dev @notice 此处暂时没有查询需求，先注释，有必要再进行优化
			// Add collection tags for the Twitter user
			// if tweet.User != nil {
			// 	collectionTags, err := h.twitterService.GetTagsByTwitterUsername(tweet.User.ScreenName)
			// 	if err != nil {
			// 		log.Error().Err(err).Str("screen_name", tweet.User.ScreenName).Msg("Error getting collection tags for user")
			// 	} else if len(collectionTags) > 0 {
			// 		tweetInfo.CollectionTags = collectionTags
			// 	}
			// }

			caResponse.Tweets = append(caResponse.Tweets, tweetInfo)
		}

		// Add token details if available
		if len(ca.TokenDetails) > 0 {
			caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
			caResponse.IsRecognized = true

			// Process each token detail
			for _, tokenDetail := range ca.TokenDetails {
				tdResponse := &TokenDetailsResponse{
					Name:         tokenDetail.TokenName,
					Symbol:       tokenDetail.Symbol,
					ChainID:      tokenDetail.ChainID,
					Source:       tokenDetail.Source,
					LogoURL:      tokenDetail.TokenLogoURL,
					TwitterURL:   tokenDetail.TokenTwitterURL,
					HolderCount:  tokenDetail.HolderCount,
					MarketCapUSD: tokenDetail.MarketCapUSD,
					PriceUSD:     tokenDetail.PriceUSD,
				}

				// Format pair created at time
				if tokenDetail.PairCreatedAt != nil {
					tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
				}

				caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
			}
		}

		response = append(response, caResponse)
	}

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

func (h *Handler) DeleteRecognizedCA(c *gin.Context) {
	// Get address from URL
	address := c.Param("address")
	if address == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Address is required"})
		return
	}

	// Delete recognized CA
	err := h.caService.DeleteRecognizedCA(address)
	if err != nil {
		log.Error().Err(err).Msg("Error deleting recognized CA")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error deleting recognized CA"})
		return
	}

	// Return success
	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// WebhookPayload represents the structure of the webhook payload
type WebhookPayload struct {
	Event string         `json:"event"`
	Data  services.Tweet `json:"data"`
	Meta  struct {
		MonitorID      string `json:"monitor_id"`
		MonitorType    string `json:"monitor_type"`
		MonitoredQuery string `json:"monitored_query"`
	} `json:"meta"`
}

// HandleWebhook godoc
// @Summary      Handle Twitter webhook
// @Description  Processes webhook events from Twitter
// @Tags         webhooks
// @Accept       json
// @Produce      json
// @Success      200
// @Router       /webhook/twitter [post]
func (h *Handler) HandleWebhook(c *gin.Context) {
	// Parse the webhook payload
	var payload WebhookPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		log.Error().Err(err).Msg("Error parsing webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook payload"})
		return
	}

	// Verify this is a new tweet event
	if payload.Event != "new_tweet" {
		log.Info().Str("event", payload.Event).Msg("Received non-tweet webhook event")
		c.Status(http.StatusOK) // Still return 200 to acknowledge receipt
		return
	}

	log.Info().
		Str("tweet_id", payload.Data.IdStr).
		Str("user", payload.Data.User.ScreenName).
		Str("event", payload.Event).
		Msg("Received tweet webhook")

	switch {
	case strings.HasPrefix(payload.Meta.MonitoredQuery, "list:"):
		parts := strings.Split(strings.TrimPrefix(payload.Meta.MonitoredQuery, "list:"), " ")
		listID := parts[0]
		if listID == "" {
			log.Warn().Msgf("Received list query with empty ID: %s", payload.Meta.MonitoredQuery)
			break // Break from switch, do not process further
		}

		var listType string
		foundList := false
		for _, listConfig := range h.twitterService.Config.TwitterLists {
			if listConfig.ID == listID {
				listType = listConfig.Type
				foundList = true
				break
			}
		}

		if foundList && listType != "" {
			log.Info().Msgf("Processing tweet for list ID: %s, Type: %s", listID, listType)
			go h.twitterService.ProcessListTweet(payload.Data, listType)
		} else {
			log.Warn().Msgf("No matching Twitter list configuration found for ID: %s from MonitoredQuery: %s. Payload: %+v", listID, payload.Meta.MonitoredQuery, payload.Data)
		}
	default:
		// Process the tweet with default webhook tag
		log.Info().Msgf("Processing tweet with default webhook tag: %s", payload.Meta.MonitoredQuery)
		go h.twitterService.ProcessTweet(payload.Data, payload.Meta.MonitoredQuery)
	}

	// Return success
	c.Status(http.StatusOK)
}

// GetRecognizedCAByAddressAndChainID godoc
// @Summary      Get a single recognized contract address
// @Description  Retrieves a specific recognized contract address by its address and chain ID
// @Tags         contract-addresses
// @Accept       json
// @Produce      json
// @Param        address  path      string  true  "Contract address"
// @Param        chain_id path      string  true  "Chain ID"
// @Success      200     {object}  CAResponse
// @Failure      404     {object}  map[string]string
// @Failure      500     {object}  map[string]string
// @Router       /recognized-ca/{address}/{chain_id} [get]
func (h *Handler) GetRecognizedCAByAddressAndChainID(c *gin.Context) {
	// Get address and chain_id from URL
	address := c.Param("address")
	chainID := c.Param("chain_id")

	if address == "" || chainID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Address and chain ID are required"})
		return
	}

	// Get recognized CA from the database
	ca, err := h.caService.GetRecognizedCAByAddressAndChainID(address, chainID)
	if err != nil {
		log.Error().Err(err).Msg("Error getting recognized CA")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting recognized CA"})
		return
	}

	// If no CA found, return 404
	if ca == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Recognized CA not found"})
		return
	}

	// Convert to response format
	caResponse := CAResponse{
		Address:   ca.CAAddress,
		ChainType: ca.ChainType,
	}

	for _, tag := range ca.Tags {
		caResponse.Tags = append(caResponse.Tags, tag.Name)
	}

	for _, tweet := range ca.Tweets {
		// Extract images from tweet JSON
		images := extractImagesFromTweet(tweet.FullTweetJSON)

		tweetInfo := TweetInfo{
			ID:          tweet.TweetID,
			Text:        tweet.TextContent,
			PublishedAt: tweet.PublishedAt.Unix(),
			User: UserResponse{
				ID:              tweet.User.UserID,
				Name:            tweet.User.Name,
				ScreenName:      tweet.User.ScreenName,
				ProfileImageURL: tweet.User.ProfileImageURL,
				IsVerified:      tweet.User.IsVerified,
				FollowersCount:  tweet.User.FollowersCount,
			},
			Images: images,
		}

		// Add tweet tags if available
		if len(tweet.Tags) > 0 {
			tweetInfo.Tags = make([]string, 0, len(tweet.Tags))
			for _, tag := range tweet.Tags {
				tweetInfo.Tags = append(tweetInfo.Tags, tag.Name)
			}
		}

		// Add collection tags if available
		if len(tweet.CollectionTags) > 0 {
			tweetInfo.CollectionTags = tweet.CollectionTags
		}

		caResponse.Tweets = append(caResponse.Tweets, tweetInfo)
	}

	// Add token details if available
	if len(ca.TokenDetails) > 0 {
		caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
		caResponse.IsRecognized = true

		// Process each token detail
		for _, tokenDetail := range ca.TokenDetails {
			tdResponse := &TokenDetailsResponse{
				Name:         tokenDetail.TokenName,
				Symbol:       tokenDetail.Symbol,
				ChainID:      tokenDetail.ChainID,
				Source:       tokenDetail.Source,
				LogoURL:      tokenDetail.TokenLogoURL,
				TwitterURL:   tokenDetail.TokenTwitterURL,
				HolderCount:  tokenDetail.HolderCount,
				MarketCapUSD: tokenDetail.MarketCapUSD,
				PriceUSD:     tokenDetail.PriceUSD,
			}

			// Format pair created at time
			if tokenDetail.PairCreatedAt != nil {
				tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
			}

			caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
		}
	}

	// Return JSON response
	c.JSON(http.StatusOK, caResponse)
}

// GetTweetByID godoc
// @Summary      Get a tweet by ID
// @Description  Retrieves a single tweet by its ID
// @Tags         tweets
// @Accept       json
// @Produce      json
// @Param        tweet_id path      string  true  "Tweet ID"
// @Success      200     {object}  TweetResponse
// @Failure      404     {object}  map[string]string
// @Failure      500     {object}  map[string]string
// @Router       /tweet/{tweet_id} [get]
func (h *Handler) GetTweetByID(c *gin.Context) {
	// Get tweet_id from URL
	tweetID := c.Param("tweet_id")

	if tweetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tweet ID is required"})
		return
	}

	// Get tweet from the database
	tweet, err := h.twitterService.GetTweetByID(tweetID)
	if err != nil {
		log.Error().Err(err).Str("tweet_id", tweetID).Msg("Error getting tweet")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting tweet"})
		return
	}

	// If no tweet found, return 404
	if tweet == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Tweet not found"})
		return
	}

	// Extract images from tweet JSON
	images := extractImagesFromTweet(tweet.FullTweetJSON)

	// Convert to response format
	tweetResponse := TweetResponse{
		TweetInfo: TweetInfo{
			ID:          tweet.TweetID,
			Text:        tweet.TextContent,
			PublishedAt: tweet.PublishedAt.Unix(),
			User: UserResponse{
				ID:              tweet.User.UserID,
				Name:            tweet.User.Name,
				ScreenName:      tweet.User.ScreenName,
				ProfileImageURL: tweet.User.ProfileImageURL,
				IsVerified:      tweet.User.IsVerified,
				FollowersCount:  tweet.User.FollowersCount,
			},
			Images:             images,
			ReplyCount:         tweet.ReplyCount,
			RetweetCount:       tweet.RetweetCount,
			FavoriteCount:      tweet.FavoriteCount,
			ViewsCount:         tweet.ViewsCount,
			BookmarkCount:      tweet.BookmarkCount,
			ContentType:        tweet.ContentType,
			ArticleTitle:       tweet.ArticleTitle,
			ArticlePreviewText: tweet.ArticlePreviewText,
			ArticleCoverURL:    tweet.ArticleCoverURL,
		},
		ContractAddresses: []CAResponse{},
	}

	// Add tags if available
	if len(tweet.Tags) > 0 {
		tweetResponse.TweetInfo.Tags = make([]string, 0, len(tweet.Tags))
		for _, tag := range tweet.Tags {
			tweetResponse.TweetInfo.Tags = append(tweetResponse.TweetInfo.Tags, tag.Name)
		}
	}

	// Add collection tags if available
	if len(tweet.CollectionTags) > 0 {
		tweetResponse.TweetInfo.CollectionTags = tweet.CollectionTags
	}

	// Add contract addresses if available
	if len(tweet.ExtractedCAs) > 0 {
		for _, ca := range tweet.ExtractedCAs {
			caResponse := CAResponse{
				Address:   ca.CAAddress,
				ChainType: ca.ChainType,
			}

			// Add token details if available
			if len(ca.TokenDetails) > 0 {
				caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
				caResponse.IsRecognized = true

				// Process each token detail
				for _, tokenDetail := range ca.TokenDetails {
					tdResponse := &TokenDetailsResponse{
						Name:         tokenDetail.TokenName,
						Symbol:       tokenDetail.Symbol,
						ChainID:      tokenDetail.ChainID,
						Source:       tokenDetail.Source,
						LogoURL:      tokenDetail.TokenLogoURL,
						TwitterURL:   tokenDetail.TokenTwitterURL,
						HolderCount:  tokenDetail.HolderCount,
						MarketCapUSD: tokenDetail.MarketCapUSD,
						PriceUSD:     tokenDetail.PriceUSD,
					}

					// Format pair created at time
					if tokenDetail.PairCreatedAt != nil {
						tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
					}

					caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
				}
			}

			tweetResponse.ContractAddresses = append(tweetResponse.ContractAddresses, caResponse)
		}
	}

	// Return JSON response
	c.JSON(http.StatusOK, tweetResponse)
}

// HealthCheck godoc
// @Summary      Health check endpoint
// @Description  Returns 200 OK if the service is healthy
// @Tags         system
// @Accept       json
// @Produce      json
// @Success      200  {object}  map[string]string
// @Router       /health [get]
func (h *Handler) HealthCheck(c *gin.Context) {
	// Simple health check that returns 200 OK
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// GetListTweets godoc
// @Summary      Get AI Agent related tweets
// @Description  Retrieves a list of tweets related to AI Agent with filtering options
// @Tags         tweets
// @Accept       json
// @Produce      json
// @Param        limit         query     int     false  "Number of tweets to return (default: 20)"
// @Param        offset        query     int     false  "Offset for pagination (default: 0)"
// @Param        content_type  query     string  false  "Filter by content type (tweet, article, ALL)"
// @Param        source_type   query     string  false  "Filter by source type (KOLs, Projects, ALL)"
// @Param        notice_type   query     string  false  "Filter by notice_type"
// @Param        user_id       query     string  false  "Filter by user_id"
// @Param        user_name       query     string  false  "Filter by user_name"
// @Param        tags    query     []string  false  "Filter by tags (comma-separated, e.g. 'tag1,tag2')"
// @Param        collection_tags    query     []string  false  "Filter by collection tags (comma-separated, e.g. 'tag1,tag2')"
// @Success      200          {array}   TweetResponse
// @Failure      500          {object}  map[string]string
// @Router       /list-tweets [get]
func (h *Handler) GetListTweets(c *gin.Context) {
	// Parse query parameters
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")
	contentType := c.Query("content_type")
	sourceType := c.Query("source_type")
	noticeType := c.Query("notice_type")
	userId := c.Query("user_id")
	userName := c.Query("user_name")
	tagsStr := c.Query("tags")
	collectionTagsStr := c.Query("collection_tags")

	limit := 20 // Default limit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // Default offset
	if offsetStr != "" {
		parsedOffset, err := strconv.Atoi(offsetStr)
		if err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	// Parse tags parameter (comma-separated)
	var tags []string
	if tagsStr != "" {
		tags = strings.Split(tagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range tags {
			tags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("tags", tags).Msg("Filtering CAs by tags")
	}

	// Parse collection_tags parameter (comma-separated)
	var collectionTags []string
	if collectionTagsStr != "" {
		collectionTags = strings.Split(collectionTagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range collectionTags {
			collectionTags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("collection_tags", collectionTags).Msg("Filtering tweets by collection tags")
	}

	// Get AI Agent tweets from the database with filtering
	req := db.GetListTweetsRequest{
		Limit:          limit,
		Offset:         offset,
		ContentType:    contentType,
		SourceType:     sourceType,
		NoticeType:     noticeType,
		UserID:         userId,
		UserName:       userName,
		Tags:           tags,
		CollectionTags: collectionTags,
	}
	tweets, err := h.twitterService.GetListTweets(req)
	if err != nil {
		log.Error().Err(err).Msg("Error getting AI Agent tweets")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting AI Agent tweets"})
		return
	}

	// Initialize CA responses slice
	var caResponses []CAResponse

	// Handle user_name parameter to get CA information
	if userName != "" && len(tweets) > 0 {
		// 1. Get CA by username
		caByUserResp, err := h.getCaByUserName(userName)
		if err != nil {
			log.Error().Err(err).Str("username", userName).Msg("Error getting CA by username")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting CA by username"})
			return
		}

		// 2. Get token details for each CA address and merge into single response
		var tradeTokenDetails []TradeTokenInfo
		var mainAddress string
		var mainChainType string
		seenChains := make(map[int]bool) // Track processed chain IDs to avoid duplicates

		for _, caData := range caByUserResp.Data.Data {
			tokenResp, err := h.getTokenInfo(caData.Address)
			if err != nil {
				log.Error().Err(err).Str("address", caData.Address).Msg("Error getting token info")
				continue
			}

			// Convert to TokenDetailsResponse format
			if len(tokenResp.Data.List) > 0 {
				tokenData := tokenResp.Data.List[0]

				// Skip if we've already processed this chain ID
				if seenChains[tokenData.ChainId] {
					continue
				}
				seenChains[tokenData.ChainId] = true

				// Set main address and chain type from first valid token
				if mainAddress == "" {
					mainAddress = tokenData.Address
					switch tokenData.ChainId {
					case 10000:
						mainChainType = "solana"
					default:
						mainChainType = "evm"
					}
				}

				tradeTokenDetails = append(tradeTokenDetails, tokenResp.Data.List...)
			}
		}

		// Create single CAResponse with all token details
		if len(tradeTokenDetails) > 0 {
			caResponse := CAResponse{
				Address:           mainAddress,
				ChainType:         mainChainType,
				IsRecognized:      true,
				TradeTokenDetails: tradeTokenDetails,
			}
			caResponses = append(caResponses, caResponse)
		}

	}

	// Convert to response format
	response := make([]TweetResponse, 0, len(tweets))
	for _, tweet := range tweets {
		// Extract images from tweet JSON
		images := extractImagesFromTweet(tweet.FullTweetJSON)

		// Create tweet response
		tweetResponse := TweetResponse{
			TweetInfo: TweetInfo{
				ID:          tweet.TweetID,
				Text:        tweet.TextContent,
				PublishedAt: tweet.PublishedAt.Unix(),
				User: UserResponse{
					ID:              tweet.User.UserID,
					Name:            tweet.User.Name,
					ScreenName:      tweet.User.ScreenName,
					ProfileImageURL: tweet.User.ProfileImageURL,
					IsVerified:      tweet.User.IsVerified,
					FollowersCount:  tweet.User.FollowersCount,
				},
				Images:             images,
				ReplyCount:         tweet.ReplyCount,
				RetweetCount:       tweet.RetweetCount,
				FavoriteCount:      tweet.FavoriteCount,
				ViewsCount:         tweet.ViewsCount,
				BookmarkCount:      tweet.BookmarkCount,
				ContentType:        tweet.ContentType,
				SourceListType:     tweet.SourceListType,
				ArticleTitle:       tweet.ArticleTitle,
				ArticlePreviewText: tweet.ArticlePreviewText,
				ArticleCoverURL:    tweet.ArticleCoverURL,
				BulletPoints:       tweet.BulletPoints,
			},
		}

		// Add tweet tags if available
		if len(tweet.Tags) > 0 {
			tweetResponse.TweetInfo.Tags = make([]string, 0, len(tweet.Tags))
			for _, tag := range tweet.Tags {
				tweetResponse.TweetInfo.Tags = append(tweetResponse.TweetInfo.Tags, tag.Name)
			}
		}

		// Add collection tags if available
		if len(tweet.CollectionTags) > 0 {
			tweetResponse.TweetInfo.CollectionTags = tweet.CollectionTags
		}

		if tweet.SourceListType != "KOLs" {
			tweetResponse.Notices = NoticeResponse{
				IsProductUpdate:        tweet.IsProductUpdate,
				IsBusinessData:         tweet.IsBusinessData,
				IsEcosystemPartnership: tweet.IsEcosystemPartnership,
				IsProfitOpportunity:    tweet.IsProfitOpportunity,
				IsIndustryEvent:        tweet.IsIndustryEvent,
				IsOthers:               tweet.IsOthers,
			}
		}

		// Add CA responses to tweet if available for username queries
		if userName != "" && len(caResponses) > 0 {
			tweetResponse.ContractAddresses = caResponses
		}

		response = append(response, tweetResponse)
	}

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

// UserAnnouncementStatsResponse represents the announcement statistics for a specific Twitter user
type UserAnnouncementStatsResponse struct {
	TwitterUser               TwitterUserInfo `json:"twitter_user"`
	ProductUpdatesCount       int64           `json:"product_updates_count"`
	BusinessDataCount         int64           `json:"business_data_count"`
	EcosystemPartnershipCount int64           `json:"ecosystem_partnership_count"`
	ProfitOpportunityCount    int64           `json:"profit_opportunity_count"`
	IndustryEventsCount       int64           `json:"industry_events_count"`
	OthersCount               int64           `json:"others_count"`
	TotalAnnouncementsCount   int64           `json:"total_announcements_count"`
}

// TwitterUserInfo represents basic Twitter user information
type TwitterUserInfo struct {
	UserID          string   `json:"user_id"`
	ScreenName      string   `json:"screen_name"`
	Name            string   `json:"name"`
	ProfileImageURL string   `json:"profile_image_url"`
	Tags            []string `json:"tags"`
}

// GetAnnouncementStatistics handles GET /api/announcement-statistics
// @Summary Get announcement statistics
// @Description Get statistics for tweets with source_list_type="Projects" and ai_judgment="YES" grouped by Twitter user. Optional time filtering with start_time and end_time query parameters (Unix timestamps). Optional sorting with sort_field and sort_direction parameters.
// @Tags statistics
// @Accept json
// @Produce json
// @Param start_time query int false "Start time (Unix timestamp, inclusive)" example(1640995200)
// @Param end_time query int false "End time (Unix timestamp, inclusive)" example(1672531199)
// @Param sort_field query string false "Field to sort by" Enums(product_updates, business_data, ecosystem_partnership, profit_opportunity, industry_events, others, total)
// @Param sort_direction query string false "Sort direction" Enums(asc, desc)
// @Success 200 {array} UserAnnouncementStatsResponse
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /announcement-statistics [get]
func (h *Handler) GetAnnouncementStatistics(c *gin.Context) {
	log.Info().Msg("Getting announcement statistics")

	// Parse optional time parameters
	var startTime, endTime *int64

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTimeVal, err := strconv.ParseInt(startTimeStr, 10, 64); err != nil {
			log.Error().Err(err).Str("start_time", startTimeStr).Msg("Invalid start_time parameter")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_time parameter. Must be a valid Unix timestamp."})
			return
		} else {
			startTime = &startTimeVal
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTimeVal, err := strconv.ParseInt(endTimeStr, 10, 64); err != nil {
			log.Error().Err(err).Str("end_time", endTimeStr).Msg("Invalid end_time parameter")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_time parameter. Must be a valid Unix timestamp."})
			return
		} else {
			endTime = &endTimeVal
		}
	}

	// Validate time range if both parameters are provided
	if startTime != nil && endTime != nil && *startTime > *endTime {
		log.Error().Int64("start_time", *startTime).Int64("end_time", *endTime).Msg("Invalid time range: start_time is greater than end_time")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid time range: start_time must be less than or equal to end_time."})
		return
	}

	// Parse and validate sorting parameters
	sortField := c.Query("sort_field")
	sortDirection := c.Query("sort_direction")

	if sortField == "" {
		sortField = "total"
	}
	if sortDirection == "" {
		sortDirection = "desc"
	}

	// Validate sort_field if provided
	validSortFields := map[string]bool{
		"product_updates":       true,
		"business_data":         true,
		"ecosystem_partnership": true,
		"profit_opportunity":    true,
		"industry_events":       true,
		"others":                true,
		"total":                 true,
	}
	if !validSortFields[sortField] {
		log.Error().Str("sort_field", sortField).Msg("Invalid sort_field parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sort_field parameter. Valid values are: product_updates, business_data, ecosystem_partnership, profit_opportunity, industry_events, others, total."})
		return
	}

	// Validate sort_direction if provided
	if sortDirection != "asc" && sortDirection != "desc" {
		log.Error().Str("sort_direction", sortDirection).Msg("Invalid sort_direction parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sort_direction parameter. Valid values are: asc, desc."})
		return
	}

	// Get announcement statistics from the database
	userStats, err := h.twitterService.GetAnnouncementStatistics(startTime, endTime)
	if err != nil {
		log.Error().Err(err).Msg("Error getting announcement statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting announcement statistics"})
		return
	}

	// Convert to response format
	response := make([]UserAnnouncementStatsResponse, 0, len(userStats))
	for _, userStat := range userStats {
		// Get tags from cached collections data based on screen_name
		tags := h.getTagsFromCacheByTwitterUsername(userStat.ScreenName)

		response = append(response, UserAnnouncementStatsResponse{
			TwitterUser: TwitterUserInfo{
				UserID:          userStat.UserID,
				ScreenName:      userStat.ScreenName,
				Name:            userStat.Name,
				ProfileImageURL: userStat.ProfileImageURL,
				Tags:            tags,
			},
			ProductUpdatesCount:       userStat.ProductUpdatesCount,
			BusinessDataCount:         userStat.BusinessDataCount,
			EcosystemPartnershipCount: userStat.EcosystemPartnershipCount,
			ProfitOpportunityCount:    userStat.ProfitOpportunityCount,
			IndustryEventsCount:       userStat.IndustryEventsCount,
			OthersCount:               userStat.OthersCount,
			TotalAnnouncementsCount:   userStat.TotalAnnouncementsCount,
		})
	}

	// Apply sorting if sort_field is provided
	sort.Slice(response, func(i, j int) bool {
		var valueI, valueJ int64

		// Get the values to compare based on sort_field
		switch sortField {
		case "product_updates":
			valueI = response[i].ProductUpdatesCount
			valueJ = response[j].ProductUpdatesCount
		case "business_data":
			valueI = response[i].BusinessDataCount
			valueJ = response[j].BusinessDataCount
		case "ecosystem_partnership":
			valueI = response[i].EcosystemPartnershipCount
			valueJ = response[j].EcosystemPartnershipCount
		case "profit_opportunity":
			valueI = response[i].ProfitOpportunityCount
			valueJ = response[j].ProfitOpportunityCount
		case "industry_events":
			valueI = response[i].IndustryEventsCount
			valueJ = response[j].IndustryEventsCount
		case "others":
			valueI = response[i].OthersCount
			valueJ = response[j].OthersCount
		case "total":
			valueI = response[i].TotalAnnouncementsCount
			valueJ = response[j].TotalAnnouncementsCount
		}

		// Apply sort direction
		if sortDirection == "asc" {
			return valueI < valueJ
		}
		return valueI > valueJ // desc is default
	})

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

// extractImagesFromTweet extracts image URLs from a tweet
func extractImagesFromTweet(tweetJSON db.JSONB) []string {
	images := []string{}
	// Use a map to track unique URLs
	uniqueURLs := make(map[string]struct{})

	// Check if entities and media exist
	entities, ok := tweetJSON["entities"].(map[string]interface{})
	if !ok {
		return images
	}

	media, ok := entities["media"].([]interface{})
	if !ok {
		return images
	}

	// Extract image URLs
	for _, m := range media {
		mediaObj, ok := m.(map[string]interface{})
		if !ok {
			continue
		}

		mediaURL, ok := mediaObj["media_url_https"].(string)
		if !ok {
			continue
		}

		// Only add if we haven't seen this URL before
		if _, exists := uniqueURLs[mediaURL]; !exists {
			uniqueURLs[mediaURL] = struct{}{}
			images = append(images, mediaURL)
		}
	}

	return images
}

// getCaByUserName calls the external API to get CA by username
func (h *Handler) getCaByUserName(userName string) (*GetCaByUserNameResponse, error) {
	client := &http.Client{Timeout: 10 * time.Second}
	apiURL := fmt.Sprintf("https://api.scattering.io/api/v3/tokens/by_tweet_username?twitter_username=%s", url.QueryEscape(userName))

	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to call CA by username API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	var result GetCaByUserNameResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// getTokenInfo calls the external API to get token information
func (h *Handler) getTokenInfo(address string) (*GetTradeTokenInfoResponse, error) {
	client := &http.Client{Timeout: 10 * time.Second}
	apiURL := fmt.Sprintf("https://scattering.io/api/collections?page=1&page_size=10&sort_field=total_volume_in_24hours&chain_id=&sort_direction=desc&name_like=%s", url.QueryEscape(address))

	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to call token info API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	var result GetTradeTokenInfoResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// getTokenInfoFromCache searches for token information in the cached collections data
func (h *Handler) getTokenInfoFromCache(address string) (*GetTradeTokenInfoResponse, error) {
	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get cached collections, falling back to API")
		// Fallback to API call if cache is not available
		return h.getTokenInfo(address)
	}

	// Search for matching tokens by address (case-insensitive)
	var matchingTokens []TradeTokenInfo
	searchAddress := strings.ToLower(address)

	for _, collection := range collections {
		if strings.ToLower(collection.Address) == searchAddress ||
			strings.Contains(strings.ToLower(collection.Address), searchAddress) ||
			strings.Contains(strings.ToLower(collection.Name), searchAddress) ||
			strings.Contains(strings.ToLower(collection.Symbol), searchAddress) {
			matchingTokens = append(matchingTokens, collection)
		}
	}

	// If no matches found in cache, fallback to API
	if len(matchingTokens) == 0 {
		log.Debug().
			Str("address", address).
			Msg("No matching tokens found in cache, falling back to API")
		return h.getTokenInfo(address)
	}

	// Sort by total volume in 24 hours (descending) to match API behavior
	sort.Slice(matchingTokens, func(i, j int) bool {
		vol1, _ := strconv.ParseFloat(matchingTokens[i].TotalVolumeIn24Hours, 64)
		vol2, _ := strconv.ParseFloat(matchingTokens[j].TotalVolumeIn24Hours, 64)
		return vol1 > vol2
	})

	// Limit to 10 results to match API behavior
	if len(matchingTokens) > 10 {
		matchingTokens = matchingTokens[:10]
	}

	// Create response in the same format as the API
	response := &GetTradeTokenInfoResponse{
		Code: 200,
		Data: struct {
			List       []TradeTokenInfo `json:"list"`
			Page       int              `json:"page"`
			PageSize   int              `json:"page_size"`
			TotalCount int              `json:"total_count"`
		}{
			List:       matchingTokens,
			Page:       1,
			PageSize:   len(matchingTokens),
			TotalCount: len(matchingTokens),
		},
		Msg: "success",
	}

	log.Debug().
		Str("address", address).
		Int("matches_found", len(matchingTokens)).
		Msg("Found matching tokens in cache")

	return response, nil
}

type GetCaByUserNameResponse struct {
	Code int `json:"code"`
	Data struct {
		Data []struct {
			Address string `json:"address"`
			ChainId int    `json:"chain_id"`
		} `json:"data"`
		TotalCount int `json:"total_count"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type GetTradeTokenInfoResponse struct {
	Code int `json:"code"`
	Data struct {
		List       []TradeTokenInfo `json:"list"`
		Page       int              `json:"page"`
		PageSize   int              `json:"page_size"`
		TotalCount int              `json:"total_count"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type TradeTokenInfo struct {
	ChainId           int       `json:"chain_id"`
	Address           string    `json:"address"`
	Name              string    `json:"name"`
	Symbol            string    `json:"symbol"`
	Decimals          int       `json:"decimals"`
	TotalSupply       string    `json:"total_supply"`
	MarketCap         string    `json:"market_cap"`
	Status            int       `json:"status"`
	Type              int       `json:"type"`
	LogoUrl           string    `json:"logo_url"`
	CreationDate      time.Time `json:"creation_date"`
	IsVerified        bool      `json:"is_verified"`
	Slug              string    `json:"slug"`
	Description       string    `json:"description"`
	BannerUrl         string    `json:"banner_url"`
	MobileBannerUrl   string    `json:"mobile_banner_url"`
	DiscordUrl        string    `json:"discord_url"`
	TelegramUrl       string    `json:"telegram_url"`
	CreatorXUsername  string    `json:"creator_x_username"`
	InstagramUsername string    `json:"instagram_username"`
	MediumUrl         string    `json:"medium_url"`
	TiktokUrl         string    `json:"tiktok_url"`
	RedditUrl         string    `json:"reddit_url"`
	WarpcastUrl       string    `json:"warpcast_url"`
	CoinmarketcapUrl  string    `json:"coinmarketcap_url"`
	CoingeckoUrl      string    `json:"coingecko_url"`
	GithubUrl         string    `json:"github_url"`
	Profile           string    `json:"profile"`
	ResearchReport    string    `json:"research_report"`
	AiReport          string    `json:"ai_report"`
	TwitterUsername   string    `json:"twitter_username"`
	TwitterUserId     string    `json:"twitter_user_id"`
	Tags              []struct {
		Name  string `json:"name"`
		Color string `json:"color"`
		Rank  int    `json:"rank"`
		Type  int    `json:"type"`
	} `json:"tags"`
	PriceInUsd              string `json:"price_in_usd"`
	TotalLiquidity          string `json:"total_liquidity"`
	Rank                    int    `json:"rank"`
	IsWatched               bool   `json:"is_watched"`
	PriceChangeIn24Hours    string `json:"price_change_in_24hours"`
	PriceChangeIn6Hours     string `json:"price_change_in_6hours"`
	PriceChangeIn1Hours     string `json:"price_change_in_1hours"`
	TotalVolumeIn24Hours    string `json:"total_volume_in_24hours"`
	TotalVolumeIn6Hours     string `json:"total_volume_in_6hours"`
	TotalVolumeIn1Hours     string `json:"total_volume_in_1hours"`
	TotalTxCount24Hours     string `json:"total_tx_count_24hours"`
	TotalBuyCount24Hours    string `json:"total_buy_count_24hours"`
	TotalSellCount24Hours   string `json:"total_sell_count_24hours"`
	TotalMakersCount24Hours string `json:"total_makers_count_24hours"`
	TotalBuyerCount24Hours  string `json:"total_buyer_count_24hours"`
	TotalSellerCount24Hours string `json:"total_seller_count_24hours"`
	ProjectUrl              string `json:"project_url"`
	TwitterScore            string `json:"twitter_score"`
	FollowersCount          int    `json:"followers_count"`
	InfluencersCount        int    `json:"influencers_count"`
	ProjectsCount           int    `json:"projects_count"`
	VentureCapitalsCount    int    `json:"venture_capitals_count"`
	Top20Followers          []struct {
		Name     string `json:"name"`
		Avatar   string `json:"avatar"`
		Username string `json:"username"`
	} `json:"top_20_followers"`
}

// GetTelegramNotificationStats returns statistics about Telegram notifications
// @Summary Get Telegram notification statistics
// @Description Returns statistics about Telegram notifications including total sent, duplicates prevented, etc.
// @Tags notifications
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Notification statistics"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /api/telegram-notification-stats [get]
func (h *Handler) GetTelegramNotificationStats(c *gin.Context) {
	stats, err := h.twitterService.GetTelegramNotificationStats()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get Telegram notification statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notification statistics"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// StartScheduler starts the cron scheduler for periodic tasks
func (h *Handler) StartScheduler() {
	if h.scheduler == nil {
		log.Error().Msg("Scheduler is not initialized")
		return
	}

	h.collectionsScheduledTask()

	// Add collections cache update job - runs every 5 days at 2 AM
	_, err := h.scheduler.AddFunc("0 2 */5 * *", h.collectionsScheduledTask)
	if err != nil {
		log.Error().Err(err).Msg("Failed to add collections cache update job")
		return
	}

	// Start the scheduler
	h.scheduler.Start()
	log.Info().Msg("Cron scheduler started with collections cache update job")
}

// StopScheduler stops the cron scheduler
func (h *Handler) StopScheduler() {
	if h.scheduler != nil {
		h.scheduler.Stop()
		log.Info().Msg("Cron scheduler stopped")
	}
}

// collectionsScheduledTask is the scheduled task that fetches and caches collections data
func (h *Handler) collectionsScheduledTask() {
	log.Info().Msg("Starting scheduled collections cache update task")

	start := time.Now()
	defer func() {
		duration := time.Since(start)
		log.Info().Dur("duration", duration).Msg("Completed scheduled collections cache update task")
	}()

	// Fetch collections data from API
	collections, err := h.fetchCollectionsData()
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch collections data")
		return
	}

	// Filter collections with tags
	filteredCollections := make([]TradeTokenInfo, 0)
	for _, collection := range collections {
		if len(collection.Tags) > 0 {
			filteredCollections = append(filteredCollections, collection)
		}
	}

	log.Info().
		Int("total_collections", len(collections)).
		Int("filtered_collections", len(filteredCollections)).
		Msg("Filtered collections by tags")

	// Cache the filtered data in Redis
	if err := h.cacheCollectionsData(filteredCollections); err != nil {
		log.Error().Err(err).Msg("Failed to cache collections data")
		return
	}

	// Save collection tags to database
	// Convert TradeTokenInfo to interface{} for database operation
	collectionsInterface := make([]interface{}, len(filteredCollections))
	for i, collection := range filteredCollections {
		// Convert struct to map for easier processing in database layer
		collectionMap := map[string]interface{}{
			"twitter_username": collection.TwitterUsername,
			"tags":             make([]interface{}, len(collection.Tags)),
		}

		// Convert tags to interface{}
		for j, tag := range collection.Tags {
			tagMap := map[string]interface{}{
				"name": tag.Name,
			}
			collectionMap["tags"].([]interface{})[j] = tagMap
		}

		collectionsInterface[i] = collectionMap
	}

	if err := h.twitterService.SaveCollectionTags(collectionsInterface); err != nil {
		log.Error().Err(err).Msg("Failed to save collection tags to database")
		// Don't return here, continue with Redis caching
	} else {
		log.Info().
			Int("collections_processed", len(filteredCollections)).
			Msg("Successfully saved collection tags to database")
	}

	log.Info().
		Int("cached_collections", len(filteredCollections)).
		Msg("Successfully cached collections data")
}

// fetchCollectionsData fetches all collections data from scattering.io API with pagination
func (h *Handler) fetchCollectionsData() ([]TradeTokenInfo, error) {
	var allCollections []TradeTokenInfo
	page := 1
	pageSize := 100

	client := &http.Client{Timeout: 30 * time.Second}

	for {
		// Build API URL with pagination and sorting parameters
		apiURL := fmt.Sprintf("https://scattering.io/api/collections?page=%d&page_size=%d&sort_field=total_volume_in_24hours&sort_direction=desc",
			page, pageSize)

		log.Debug().
			Str("url", apiURL).
			Int("page", page).
			Msg("Fetching collections page")

		// Make HTTP request
		resp, err := client.Get(apiURL)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch collections page %d: %w", page, err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("API returned status code %d for page %d", resp.StatusCode, page)
		}

		// Parse response
		var collectionsResp GetTradeTokenInfoResponse
		if err := json.NewDecoder(resp.Body).Decode(&collectionsResp); err != nil {
			return nil, fmt.Errorf("failed to decode response for page %d: %w", page, err)
		}

		// Check API response code
		if collectionsResp.Code != 0 {
			return nil, fmt.Errorf("API returned error code %d: %s", collectionsResp.Code, collectionsResp.Msg)
		}

		// Add collections from this page
		allCollections = append(allCollections, collectionsResp.Data.List...)

		log.Debug().
			Int("page", page).
			Int("page_collections", len(collectionsResp.Data.List)).
			Int("total_so_far", len(allCollections)).
			Int("total_count", collectionsResp.Data.TotalCount).
			Msg("Fetched collections page")

		// Check if we've fetched all pages
		if len(collectionsResp.Data.List) < pageSize || len(allCollections) >= collectionsResp.Data.TotalCount {
			break
		}

		page++

		// Add a small delay between requests to be respectful to the API
		time.Sleep(100 * time.Millisecond)
	}

	log.Info().
		Int("total_collections", len(allCollections)).
		Int("total_pages", page).
		Msg("Successfully fetched all collections data")

	return allCollections, nil
}

// cacheCollectionsData caches the collections data in Redis with 7-day expiration
func (h *Handler) cacheCollectionsData(collections []TradeTokenInfo) error {
	if h.redisService == nil {
		return fmt.Errorf("redis service is not available")
	}

	// Serialize collections to JSON
	data, err := json.Marshal(collections)
	if err != nil {
		return fmt.Errorf("failed to marshal collections data: %w", err)
	}

	// Get Redis client
	client := h.redisService.GetClient()
	ctx := context.Background()

	// Cache key with versioned prefix
	cacheKey := "v2_collections_cache"

	// Set cache with 7-day expiration
	expiration := 7 * 24 * time.Hour
	err = client.Set(ctx, cacheKey, data, expiration).Err()
	if err != nil {
		return fmt.Errorf("failed to cache collections data: %w", err)
	}

	log.Info().
		Str("cache_key", cacheKey).
		Dur("expiration", expiration).
		Int("data_size_bytes", len(data)).
		Msg("Successfully cached collections data in Redis")

	return nil
}

// getCachedCollectionsData retrieves collections data from Redis cache
func (h *Handler) getCachedCollectionsData() ([]TradeTokenInfo, error) {
	if h.redisService == nil {
		return nil, fmt.Errorf("redis service is not available")
	}

	// Get Redis client
	client := h.redisService.GetClient()
	ctx := context.Background()

	// Cache key with versioned prefix
	cacheKey := "v2_collections_cache"

	// Get cached data
	cachedData, err := client.Get(ctx, cacheKey).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return nil, fmt.Errorf("collections cache not found")
		}
		return nil, fmt.Errorf("failed to retrieve collections cache: %w", err)
	}

	// Deserialize collections from JSON
	var collections []TradeTokenInfo
	if err := json.Unmarshal([]byte(cachedData), &collections); err != nil {
		return nil, fmt.Errorf("failed to unmarshal collections data: %w", err)
	}

	log.Debug().
		Str("cache_key", cacheKey).
		Int("collections_count", len(collections)).
		Msg("Successfully retrieved collections data from Redis cache")

	return collections, nil
}

// getTagsFromCacheByTwitterUsername retrieves tags for a Twitter user from cached collections data
func (h *Handler) getTagsFromCacheByTwitterUsername(screenName string) []string {
	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Debug().Err(err).Str("screen_name", screenName).Msg("Failed to get cached collections for tags lookup")
		return []string{}
	}

	// Collect unique tags for this Twitter user
	tagSet := make(map[string]bool)
	var tags []string

	// Search for collections associated with this Twitter username
	for _, collection := range collections {
		// Match by Twitter username (case-insensitive)
		if strings.EqualFold(collection.TwitterUsername, screenName) {
			// Add all tags from this collection
			for _, tag := range collection.Tags {
				if tag.Name != "" && !tagSet[tag.Name] {
					tagSet[tag.Name] = true
					tags = append(tags, tag.Name)
				}
			}
		}
	}

	log.Debug().
		Str("screen_name", screenName).
		Int("tags_count", len(tags)).
		Strs("tags", tags).
		Msg("Retrieved tags from cache for Twitter user")

	return tags
}

// TriggerCollectionsCacheUpdate manually triggers the collections cache update
// This is useful for testing or manual cache refresh
// @Summary Manually trigger collections cache update
// @Description Triggers the collections cache update task manually for testing purposes
// @Tags system
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /trigger-collections-cache [post]
func (h *Handler) TriggerCollectionsCacheUpdate(c *gin.Context) {
	log.Info().Msg("Manual collections cache update triggered")

	// Run the scheduled task in a goroutine to avoid blocking the HTTP request
	go h.collectionsScheduledTask()

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Collections cache update task triggered",
	})
}

// GetCollectionTags returns all unique collection tags
// @Summary Get all collection tags
// @Description Returns a list of all unique collection tags from cached collections
// @Tags collections
// @Accept json
// @Produce json
// @Success 200 {object} []string "Collection tags list"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /collection-tags [get]
func (h *Handler) GetCollectionTags(c *gin.Context) {
	log.Info().Msg("Getting collection tags")

	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get cached collections data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get collections data"})
		return
	}

	// Extract unique tags
	tagsMap := make(map[string]string)

	for _, collection := range collections {
		for _, tag := range collection.Tags {
			tagsMap[tag.Name] = tag.Name
		}
	}

	// Convert map to slice
	tags := make([]string, 0, len(tagsMap))

	for _, tag := range tagsMap {
		tags = append(tags, tag)
	}

	c.JSON(http.StatusOK, tags)
}

// GetCachedCollections returns collections data from Redis cache
// @Summary Get cached collections data
// @Description Retrieves collections data from Redis cache with tags information
// @Tags collections
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 100)"
// @Success 200 {object} GetTradeTokenInfoResponse
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /cached-collections [get]
func (h *Handler) GetCachedCollections(c *gin.Context) {
	log.Info().Msg("Getting cached collections data")

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "100")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 1000 {
		pageSize = 100
	}

	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get cached collections data")
		if err.Error() == "collections cache not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Collections cache not found. Please trigger cache update first.",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to retrieve cached collections data",
			})
		}
		return
	}

	// Calculate pagination
	totalCount := len(collections)
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= totalCount {
		// Return empty result if page is beyond available data
		response := GetTradeTokenInfoResponse{
			Code: 200,
			Data: struct {
				List       []TradeTokenInfo `json:"list"`
				Page       int              `json:"page"`
				PageSize   int              `json:"page_size"`
				TotalCount int              `json:"total_count"`
			}{
				List:       []TradeTokenInfo{},
				Page:       page,
				PageSize:   pageSize,
				TotalCount: totalCount,
			},
			Msg: "success",
		}
		c.JSON(http.StatusOK, response)
		return
	}

	if endIndex > totalCount {
		endIndex = totalCount
	}

	// Get paginated data
	paginatedCollections := collections[startIndex:endIndex]

	// Create response
	response := GetTradeTokenInfoResponse{
		Code: 200,
		Data: struct {
			List       []TradeTokenInfo `json:"list"`
			Page       int              `json:"page"`
			PageSize   int              `json:"page_size"`
			TotalCount int              `json:"total_count"`
		}{
			List:       paginatedCollections,
			Page:       page,
			PageSize:   pageSize,
			TotalCount: totalCount,
		},
		Msg: "success",
	}

	log.Info().
		Int("total_count", totalCount).
		Int("page", page).
		Int("page_size", pageSize).
		Int("returned_count", len(paginatedCollections)).
		Msg("Successfully returned cached collections data")

	c.JSON(http.StatusOK, response)
}
