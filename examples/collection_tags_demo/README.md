# Collection Tags 缓存功能演示

这个示例演示了如何使用 Collection Tags 的缓存功能。

## 功能特性

- Redis 缓存集成
- Collection Tags 验证和过滤
- 缓存性能对比演示

## 运行前准备

1. 确保 Redis 服务器运行在 `localhost:6379`
2. 确保 PostgreSQL 数据库运行在 `localhost:5432`
3. 数据库配置：
   - 用户名: `postgres`
   - 密码: `postgres`
   - 数据库名: `ca_service`

## 运行方式

```bash
cd examples/collection_tags_demo
go run main.go
```

## 演示内容

1. **缓存性能对比**: 第一次调用从 API 获取数据，第二次调用从 Redis 缓存获取
2. **Collection Tags 过滤**: 演示如何过滤无效的 collection tags
3. **缓存键检查**: 检查 Redis 中的缓存状态

## 注意事项

- 需要有效的网络连接来访问 Twitter API
- 确保数据库和 Redis 服务正常运行
- 首次运行可能需要较长时间来获取和缓存数据
