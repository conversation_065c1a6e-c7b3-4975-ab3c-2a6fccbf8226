# 示例程序

这个目录包含了 real-time-ca-service 项目的各种功能演示程序。

## 目录结构

```
examples/
├── collection_tags_demo/     # Collection Tags 缓存功能演示
│   ├── main.go
│   └── README.md
├── duplicate_detection_demo/ # 重复内容检测功能演示
│   ├── main.go
│   └── README.md
└── README.md                # 本文件
```

## 运行示例

每个示例都是独立的 Go 程序，可以单独运行：

### Collection Tags 缓存演示
```bash
cd collection_tags_demo
go run main.go
```

### 重复内容检测演示
```bash
cd duplicate_detection_demo
go run main.go
```

## 通用要求

- Go 1.19 或更高版本
- 项目依赖已安装（运行 `go mod tidy`）

## 特定要求

### Collection Tags 演示
- Redis 服务器（localhost:6379）
- PostgreSQL 数据库（localhost:5432）
- 网络连接（访问 Twitter API）

### 重复内容检测演示
- 无外部依赖，可直接运行

## 故障排除

如果遇到导入路径问题，请确保：
1. 在项目根目录运行 `go mod tidy`
2. Go 模块路径正确设置
3. 所有依赖项已正确安装
