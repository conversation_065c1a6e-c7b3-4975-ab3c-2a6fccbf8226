# 重复内容检测功能演示

这个示例演示了重复内容检测功能的各种特性。

## 功能特性

- 文本预处理（移除 URL、@mentions、标点符号等）
- Jaccard 相似度计算
- 余弦相似度计算
- 可配置的相似度阈值
- 不同阈值效果对比

## 运行方式

```bash
cd examples/duplicate_detection_demo
go run main.go
```

## 演示内容

1. **文本预处理演示**: 展示如何清理和标准化文本内容
2. **相似度计算演示**: 对比不同文本对的相似度分数
3. **阈值影响演示**: 展示不同阈值设置对重复检测结果的影响

## 算法说明

- **Jaccard 相似度**: 基于词集合的交集/并集比例
- **余弦相似度**: 基于词频向量的夹角余弦值
- **默认阈值**: 70%（可配置）
- **时间窗口**: 30天（只比较最近的推文）
- **性能优化**: 限制比较的推文数量为100条

## 配置参数

- `SimilarityThreshold`: 相似度阈值（0.0-1.0）
- `TimeWindowDays`: 时间窗口天数
- `MaxTweetsToCompare`: 最大比较推文数量
