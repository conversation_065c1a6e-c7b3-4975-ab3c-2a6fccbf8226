package main

import (
	"fmt"
	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/services"
)

func main() {
	fmt.Println("=== 重复内容检测功能演示 ===\n")

	// 创建重复检测器配置
	cfg := config.DuplicateDetectorConfig{
		Enabled:             true,
		SimilarityThreshold: 0.7,
		TimeWindowDays:      30,
		MaxTweetsToCompare:  100,
	}

	// 创建重复检测器实例（不需要数据库连接来演示文本处理功能）
	detector := services.NewDuplicateDetector(cfg, nil)

	// 演示文本预处理功能
	fmt.Println("1. 文本预处理演示:")
	testTexts := []string{
		"🚀 New #AI agent CA: 0x1234...abcd @everyone check https://dexscreener.com/ethereum/0x1234",
		"Check this out https://example.com/path with @username",
		"This is #awesome #crypto news!!!",
		"Hello, world! How are you?",
	}

	for _, text := range testTexts {
		processed := detector.PreprocessText(text)
		fmt.Printf("原文: %s\n", text)
		fmt.Printf("处理后: %s\n\n", processed)
	}

	// 演示相似度计算
	fmt.Println("2. 相似度计算演示:")
	similarityTests := []struct {
		text1 string
		text2 string
	}{
		{
			"New AI agent token launched on Ethereum",
			"New AI agent token released on Ethereum",
		},
		{
			"Check out this amazing new project",
			"Look at this incredible new initiative",
		},
		{
			"Bitcoin price hits new high",
			"Ethereum network upgrade completed",
		},
		{
			"Same exact text here",
			"Same exact text here",
		},
	}

	for _, test := range similarityTests {
		processed1 := detector.PreprocessText(test.text1)
		processed2 := detector.PreprocessText(test.text2)
		
		jaccardSim := detector.CalculateJaccardSimilarity(processed1, processed2)
		cosineSim := detector.CalculateCosineSimilarity(processed1, processed2)
		
		fmt.Printf("文本1: %s\n", test.text1)
		fmt.Printf("文本2: %s\n", test.text2)
		fmt.Printf("Jaccard相似度: %.3f\n", jaccardSim)
		fmt.Printf("余弦相似度: %.3f\n", cosineSim)
		
		if jaccardSim >= cfg.SimilarityThreshold {
			fmt.Printf("✅ 检测到重复内容 (阈值: %.1f)\n", cfg.SimilarityThreshold)
		} else {
			fmt.Printf("❌ 未检测到重复内容 (阈值: %.1f)\n", cfg.SimilarityThreshold)
		}
		fmt.Println()
	}

	// 演示配置的影响
	fmt.Println("3. 不同阈值的影响:")
	text1 := "New AI agent token launched"
	text2 := "New AI agent token released"
	
	processed1 := detector.PreprocessText(text1)
	processed2 := detector.PreprocessText(text2)
	similarity := detector.CalculateJaccardSimilarity(processed1, processed2)
	
	thresholds := []float64{0.5, 0.6, 0.7, 0.8, 0.9}
	
	fmt.Printf("文本相似度: %.3f\n", similarity)
	for _, threshold := range thresholds {
		if similarity >= threshold {
			fmt.Printf("阈值 %.1f: ✅ 检测为重复\n", threshold)
		} else {
			fmt.Printf("阈值 %.1f: ❌ 不是重复\n", threshold)
		}
	}

	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("\n功能说明:")
	fmt.Println("- 文本预处理: 移除URL、@mentions、标点符号，转换为小写")
	fmt.Println("- Jaccard相似度: 基于词集合的交集/并集比例")
	fmt.Println("- 余弦相似度: 基于词频向量的夹角余弦值")
	fmt.Println("- 相似度阈值: 可配置，默认70%")
	fmt.Println("- 时间窗口: 只比较最近30天的推文")
	fmt.Println("- 性能优化: 限制比较的推文数量")
}
