# Real-Time CA Service

A real-time Twitter monitoring service that tracks tweets containing "Ai agent CA", extracts contract addresses, and fetches token details for recognized CAs.

## Features

- Monitors Twitter for tweets containing "Ai agent CA"
- Extracts potential contract addresses (CAs) from tweets
- Validates CAs for different blockchain formats (EVM, Solana)
- Checks if extracted CAs are in a "recognized" list
- Fetches token details from DexScreener API for recognized CAs
- Provides API endpoints for accessing the data
- Structured logging with zerolog
- Prometheus metrics for monitoring
- Automatic database migrations
- Time-based partitioning for high-volume tables

## Architecture

The service consists of the following components:

- **Twitter Service**: Monitors Twitter for tweets containing "Ai agent CA"
- **CA Service**: Extracts and validates contract addresses from tweets
- **Token Service**: Fetches token details from DexScreener API
- **API**: Provides endpoints for accessing the data
- **PostgreSQL Database**: Stores tweets, users, CAs, and token details
- **Metrics**: Prometheus metrics for monitoring system performance
- **Logger**: Structured logging with zerolog

## API Endpoints

- `GET /api/tweets`: Get tweets containing "Ai agent CA"
- `POST /api/recognized-cas`: Add a new recognized CA
- `DELETE /api/recognized-cas/:address`: Remove a recognized CA
- `POST /api/webhook/twitter`: Webhook endpoint for Twitter (if enabled)
- `GET /api/health`: Health check endpoint
- `GET /metrics`: Prometheus metrics endpoint (if enabled)

## Requirements

- Go 1.24 or higher
- PostgreSQL 16 or higher
- Docker and Docker Compose (optional)

## Configuration

The service is configured using a TOML configuration file. An example configuration file `service.example.toml` is provided in the repository. You can copy it to `service.toml` and modify the values as needed.

### Configuration File Sections

#### Server Configuration

- `server.addr`: Server address (default: `:8080`)

#### Database Configuration

- `database.host`: PostgreSQL host (default: `localhost`)
- `database.port`: PostgreSQL port (default: `5432`)
- `database.user`: PostgreSQL user (default: `postgres`)
- `database.password`: PostgreSQL password (default: `postgres`)
- `database.name`: PostgreSQL database name (default: `ca_service`)
- `database.sslmode`: PostgreSQL SSL mode (default: `disable`)

#### SocialData.tools Configuration

- `socialdata.api_key`: SocialData.tools API key (required)
- `socialdata.base_url`: SocialData.tools API base URL (default: `https://api.socialdata.tools`)
- `socialdata.keyword`: Keyword to search for (default: `Ai agent CA`)
- `socialdata.polling_interval_sec`: Polling interval in seconds (default: `60`)
- `socialdata.requests_per_min`: Maximum requests per minute (default: `120`)
- `socialdata.webhook_enabled`: Enable webhook (default: `false`)
- `socialdata.webhook_endpoint`: Webhook endpoint (default: `/api/webhook/twitter`)
- `socialdata.webhook_secret_key`: Webhook secret key (default: ``)

#### DexScreener Configuration

- `dexscreener.base_url`: DexScreener API base URL (default: `https://api.dexscreener.com`)
- `dexscreener.requests_per_min`: Maximum requests per minute (default: `300`)
- `dexscreener.update_interval_min`: Update interval in minutes (default: `15`)
- `dexscreener.supported_chains`: Supported chains (default: `ethereum,bsc,polygon,solana`)

## Running with Docker Compose

1. Copy `service.example.toml` to `service.toml` and update it with your SocialData.tools API key and other configuration as needed.

2. Build and start the containers:

```bash
docker-compose up -d
```

3. The service will be available at <http://localhost:8080>

## Running Locally

1. Install dependencies:

```bash
go mod download
```

2. Set up the database:

```bash
psql -U postgres -c "CREATE DATABASE ca_service"
psql -U postgres -d ca_service -f migrations/001_initial_schema.sql
```

3. Copy `service.example.toml` to `service.toml` and update it with your SocialData.tools API key and other configuration as needed.

4. Run the service:

```bash
go run cmd/server/main.go
```
