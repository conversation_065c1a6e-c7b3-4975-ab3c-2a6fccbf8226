#!/bin/bash

# Test script to send a webhook request to the local server

# Ensure the webhook_example.json file exists
if [ ! -f "webhook_example.json" ]; then
  echo "Error: webhook_example.json file not found"
  exit 1
fi

# Send the webhook request
echo "Sending webhook request to http://localhost:8080/webhook/twitter"
curl -X POST \
  -H "Content-Type: application/json" \
  -d @webhook_example.json \
  http://localhost:8080/webhook/twitter

echo -e "\nWebhook request sent"
