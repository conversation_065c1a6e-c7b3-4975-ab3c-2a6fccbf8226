# 重复内容检测功能

## 概述

本功能在 Twitter 服务的推文处理流程中添加了重复内容检测和标记功能，能够自动识别同一用户发布的相似内容，并将旧的推文标记为"过时"状态。

## 功能特性

### 1. 检测时机
- 在推文处理流程的第1420行（AI判断之前）执行检测
- 每当有新推文进入处理流程时自动触发
- 不影响现有的推文处理逻辑

### 2. 检测条件
- 查询数据库中同一用户（相同 twitter_username）的所有未过时推文
- 计算新推文与现有推文的内容相似度
- 使用 Jaccard 相似度算法，默认阈值为 70%
- 支持时间窗口限制（默认30天）和数量限制（默认100条）

### 3. 处理逻辑
- 如果找到相似度超过阈值的现有推文，将该旧推文标记为"过时"
- 在数据库中添加过时标记字段：`is_outdated`、`outdated_at`、`outdated_by_tweet_id`
- 继续按现有逻辑处理新推文，不受影响
- 使用数据库事务确保操作的原子性

## 技术实现

### 1. 数据库模式扩展

在 `tweets` 表中添加了以下字段：

```sql
-- 重复内容检测字段
is_outdated BOOLEAN DEFAULT FALSE,
outdated_at TIMESTAMP WITH TIME ZONE,
outdated_by_tweet_id VARCHAR(255)
```

### 2. 核心组件

#### DuplicateDetector
- 位置：`internal/services/duplicate_detector.go`
- 功能：处理重复内容检测的核心逻辑
- 配置：支持通过 TOML 配置文件进行配置

#### 文本预处理
- 转换为小写
- 移除 URL、@mentions、#hashtags
- 去除标点符号和多余空格
- 过滤短词（长度 ≤ 2）

#### 相似度算法
- **Jaccard 相似度**：基于词集合的交集/并集比例
- **余弦相似度**：基于词频向量的夹角余弦值（备选）
- 默认使用 Jaccard 相似度，阈值 70%

### 3. 配置选项

在 `service.toml` 中添加：

```toml
[duplicate_detector]
# 启用重复内容检测
enabled = true
# 相似度阈值 (0.0-1.0)
similarity_threshold = 0.7
# 时间窗口（天）
time_window_days = 30
# 最大比较推文数量
max_tweets_to_compare = 100
```

### 4. 性能优化

- **时间窗口限制**：只比较最近 N 天的推文
- **数量限制**：限制每次比较的推文数量
- **索引优化**：为相关字段添加数据库索引
- **批量操作**：使用批量查询和更新
- **错误处理**：检测失败不影响推文处理流程

## 集成方式

### 1. TwitterService 集成
- 在 `NewTwitterService` 中初始化 `DuplicateDetector`
- 在 `processListTweet` 函数中调用检测逻辑
- 添加错误处理和日志记录

### 2. 数据库操作
- 新增 `GetUserRecentTweets` 函数
- 新增 `MarkTweetsAsOutdated` 函数
- 使用事务确保数据一致性

## 使用示例

### 1. 运行演示
```bash
go run examples/duplicate_detection_demo.go
```

### 2. 测试
```bash
go test ./internal/services -run TestDuplicateDetector -v
```

## 监控和日志

### 日志记录
- 检测开始和结束时间
- 发现的重复内容详情
- 性能指标（处理时间、比较数量）
- 错误信息和警告

### 示例日志
```json
{
  "level": "info",
  "tweet_id": "123456789",
  "outdated_tweet_id": "987654321",
  "similarity": 0.85,
  "message": "Found duplicate content, marking older tweet as outdated"
}
```

## 配置建议

### 1. 相似度阈值
- **0.5-0.6**：较宽松，可能产生误报
- **0.7-0.8**：推荐设置，平衡准确性和召回率
- **0.9+**：严格模式，只检测几乎完全相同的内容

### 2. 时间窗口
- **7天**：适用于高频发布的账户
- **30天**：默认设置，适用于大多数场景
- **90天**：适用于需要长期去重的场景

### 3. 比较数量限制
- **50条**：性能优先
- **100条**：默认设置，平衡性能和准确性
- **200条**：准确性优先

## 故障排除

### 1. 常见问题
- **检测不生效**：检查配置文件中 `enabled` 是否为 `true`
- **性能问题**：调整时间窗口和比较数量限制
- **误报过多**：降低相似度阈值
- **漏报过多**：提高相似度阈值

### 2. 调试方法
- 查看日志中的相似度计算结果
- 使用演示程序测试文本预处理效果
- 检查数据库中的过时标记字段

## 未来扩展

### 1. 算法优化
- 支持语义相似度（使用词向量）
- 支持多语言文本处理
- 支持图片和视频内容检测

### 2. 功能增强
- 支持跨用户重复检测
- 支持批量重新检测历史数据
- 支持自定义相似度算法

### 3. 性能优化
- 使用 Redis 缓存用户推文
- 使用异步处理提高吞吐量
- 使用机器学习模型提高准确性
