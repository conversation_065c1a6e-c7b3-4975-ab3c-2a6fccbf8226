# Collection Tags Validation with Redis Caching

## 概述

本功能实现了在保存 collection tags 到数据库时的验证机制，确保只有在 scattering.io API 中定义的有效 tags 才会被保存到数据库中。同时，该功能还包含了 Redis 缓存机制，用于提高性能并减少对外部 API 的依赖。

## 功能特性

### API 验证
- **API 端点**: `https://api.scattering.io/api/v3/collections/tags`
- **响应格式**: `CollectionsTagsViewsResponse` 结构体
- **验证机制**: 只保存 API 返回的有效 tag names

### Redis 缓存机制
- **缓存键**: `v2_valid_collection_tags`
- **缓存时间**: 8 天（默认）
- **缓存内容**: 有效 tag names 的 JSON 数组
- **降级处理**: Redis 不可用时自动回退到 API 调用

### 数据过滤
- **过滤条件**:
  1. Tag name 不能为空
  2. Tag name 必须存在于 API 返回的有效 tags 列表中
- **性能优化**: 使用 `map[string]bool` 进行快速查找
- **实时过滤**: 在 `PopulateCollectionTags` 方法中过滤返回的 collection tags

## 实现细节

### 核心方法

#### `fetchValidCollectionTags()`
```go
func (d *Database) fetchValidCollectionTags() (map[string]bool, error)
```

**功能**:
- 优先从 Redis 缓存中获取有效 tags
- 缓存未命中时调用 API 获取数据
- 自动将 API 结果缓存到 Redis（8天过期时间）
- 创建 `map[string]bool` 用于快速查找有效 tag names

**缓存逻辑**:
1. 检查 Redis 缓存是否存在有效 tags
2. 缓存命中：解析 JSON 数据并返回
3. 缓存未命中：调用 `fetchValidCollectionTagsFromAPI()`
4. 将 API 结果缓存到 Redis

**错误处理**:
- Redis 连接失败（自动降级到 API）
- HTTP 请求失败
- 非 200 状态码响应
- JSON 解析失败

#### `fetchValidCollectionTagsFromAPI()`
```go
func (d *Database) fetchValidCollectionTagsFromAPI() (map[string]bool, error)
```

**功能**:
- 直接调用 scattering.io API 获取有效的 collection tags
- 解析响应为 `CollectionsTagsViewsResponse` 结构体
- 创建 `map[string]bool` 用于快速查找有效 tag names

**用途**:
- 作为 `fetchValidCollectionTags()` 的后备方法
- 当 Redis 缓存不可用时的降级处理

#### `PopulateCollectionTags()` 修改
在原有方法基础上添加了实时过滤逻辑：

```go
// 获取有效 collection tags 从缓存/API
validTagsMap, err := d.fetchValidCollectionTags()
if err != nil {
    // 如果无法获取有效 tags，记录错误但继续执行（不过滤）
    log.Error().Err(err).Msg("Failed to fetch valid collection tags, proceeding without filtering")
    validTagsMap = nil
}

// 在返回 tweet collection tags 时过滤
if validTagsMap != nil {
    var filteredTags []string
    for _, tag := range tags {
        if validTagsMap[tag] {
            filteredTags = append(filteredTags, tag)
        }
    }
    tweet.CollectionTags = filteredTags
} else {
    // 无过滤，返回所有 tags
    tweet.CollectionTags = tags
}
```

**关键特性**:
- **降级处理**: API 失败时不影响正常功能，只是不进行过滤
- **实时过滤**: 每次返回 tweet 数据时都会过滤 collection tags
- **缓存优先**: 优先使用 Redis 缓存，减少 API 调用

#### `SaveCollectionTags()` 修改
在原有方法基础上添加了验证逻辑：

```go
// 获取有效 tags
validTagsMap, err := d.fetchValidCollectionTags()
if err != nil {
    return fmt.Errorf("failed to fetch valid collection tags: %w", err)
}

// 在处理每个 tag 时验证
if tagName != "" && validTagsMap[tagName] {
    // 只保存有效的 tags
    collectionTag := CollectionTag{
        TagName:         tagName,
        TwitterUsername: twitterUsername,
    }
    collectionTags = append(collectionTags, collectionTag)
}
```

### 数据结构

#### `CollectionsTagsViewsResponse`
```go
type CollectionsTagsViewsResponse struct {
    Code int `json:"code"`
    Data struct {
        List []struct {
            Name  string `json:"name"`
            Color string `json:"color"`
            Rank  int    `json:"rank"`
            Type  int    `json:"type"`
        } `json:"list"`
    } `json:"data"`
    Msg string `json:"msg"`
}
```

## 使用场景

### 数据清洗
- 过滤掉无效或过时的 tag names
- 确保数据库中只存储标准化的 tags
- 防止因外部数据源错误导致的数据污染

### 性能优化
- **Redis 缓存**: 8天缓存期，大幅减少 API 调用
- **快速查找**: 使用 `map[string]bool` 进行 O(1) 查找
- **批量处理**: 一次获取所有有效 tags，避免重复调用
- **降级处理**: Redis 不可用时自动回退，保证服务可用性
- **减少数据库负载**: 过滤无效数据，减少存储和查询开销

## 错误处理

### API 调用失败
如果 API 调用失败，整个 `SaveCollectionTags` 操作会失败并返回错误：
```go
return fmt.Errorf("failed to fetch valid collection tags: %w", err)
```

### 网络超时
HTTP 客户端配置了 30 秒超时，与项目中其他 API 调用保持一致。

## 测试

### 单元测试
- `TestCollectionTagsFiltering`: 验证数据结构和过滤逻辑
- 测试覆盖有效和无效 tag names 的处理

### 集成测试建议
1. 模拟 API 响应测试完整流程
2. 测试网络错误情况的处理
3. 验证数据库事务的正确性

## 监控和维护

### 日志记录
建议添加以下日志记录：
- API 调用成功/失败
- 过滤掉的无效 tags 数量
- 保存的有效 tags 数量

### 性能监控
- API 响应时间
- 过滤效率
- 数据库操作性能

## 配置选项

### HTTP 超时
当前设置为 30 秒，可根据需要调整：
```go
client := &http.Client{Timeout: 30 * time.Second}
```

### Redis 缓存配置
当前缓存配置：
```go
cacheKey := "v2_valid_collection_tags"
expiration := 8 * 24 * time.Hour // 8 days
```

### 错误重试
目前没有实现重试机制，如需要可以添加指数退避重试逻辑。

## 架构变更

### Database 结构体修改
```go
type Database struct {
    DB           *gorm.DB
    redisService RedisService // 新增 Redis 服务接口
}
```

### 服务初始化顺序
1. 初始化 Redis 服务
2. 使用 `ConnectWithRedis()` 创建数据库连接
3. 通过适配器模式连接 Redis 服务

### 接口设计
```go
type RedisService interface {
    GetClient() interface{} // 返回 *redis.Client
}
```

## 性能指标

### 缓存命中率
- **目标**: >90% 缓存命中率
- **监控**: 通过日志记录缓存命中/未命中情况

### 响应时间
- **缓存命中**: <10ms
- **API 调用**: <30s（HTTP 超时）
- **降级处理**: 无额外延迟

### 内存使用
- **缓存大小**: 通常 <1KB（tag names 列表）
- **过期策略**: 8天自动过期
