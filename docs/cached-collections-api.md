# Cached Collections API

## 概述

本功能实现了从 Redis 缓存中读取 collections 数据并转换为 `TradeTokenInfo` 格式的 API 端点。这样可以快速访问已缓存的 collections 数据，避免频繁调用外部 API。

## 功能特性

### 缓存数据读取
- **缓存键**: `v2_collections_cache`
- **数据格式**: JSON 序列化的 `TradeTokenInfo` 数组
- **自动回退**: 如果缓存不可用，自动回退到外部 API 调用

### Tags 信息处理
- **数据结构**: 复用现有的 `TradeTokenInfo` 结构体
- **Tags 字段**: 包含 `name`, `color`, `rank`, `type` 等信息
- **过滤条件**: 只返回包含 tags 的 collections 数据

### 智能搜索
- **地址匹配**: 支持精确和模糊地址匹配
- **名称搜索**: 支持按 token 名称和符号搜索
- **排序**: 按24小时交易量降序排列
- **限制**: 最多返回10个匹配结果

## API 端点

### 1. 获取缓存的 Collections 数据

```http
GET /api/cached-collections
```

#### 查询参数
- `page` (可选): 页码，默认为 1
- `page_size` (可选): 每页大小，默认为 100，最大 1000

#### 响应格式
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "chain_id": 1,
        "address": "0x123...",
        "name": "Token Name",
        "symbol": "TKN",
        "tags": [
          {
            "name": "defi",
            "color": "blue",
            "rank": 1,
            "type": 1
          }
        ],
        "total_volume_in_24hours": "1000000.50"
      }
    ],
    "page": 1,
    "page_size": 100,
    "total_count": 500
  },
  "msg": "success"
}
```

#### 错误响应
```json
// 缓存不存在
{
  "error": "Collections cache not found. Please trigger cache update first."
}

// 服务器错误
{
  "error": "Failed to retrieve cached collections data"
}
```

### 2. 手动触发缓存更新

```http
POST /api/trigger-collections-cache
```

#### 响应格式
```json
{
  "status": "success",
  "message": "Collections cache update task triggered"
}
```

## 核心方法

### getCachedCollectionsData()
从 Redis 缓存中读取 collections 数据。

```go
func (h *Handler) getCachedCollectionsData() ([]TradeTokenInfo, error)
```

**功能**:
- 从 Redis 读取缓存数据
- JSON 反序列化为 `TradeTokenInfo` 数组
- 错误处理和日志记录

**返回**:
- 成功: `[]TradeTokenInfo` 和 `nil`
- 失败: `nil` 和错误信息

### getTokenInfoFromCache()
从缓存中搜索特定 token 信息，支持回退到 API。

```go
func (h *Handler) getTokenInfoFromCache(address string) (*GetTradeTokenInfoResponse, error)
```

**功能**:
- 优先从缓存中搜索匹配的 token
- 支持地址、名称、符号的模糊匹配
- 按交易量排序，限制返回数量
- 缓存不可用时自动回退到 API 调用

**搜索逻辑**:
1. 精确地址匹配 (不区分大小写)
2. 地址包含匹配
3. 名称包含匹配
4. 符号包含匹配

### GetCachedCollections()
HTTP 处理函数，提供分页的缓存数据访问。

```go
func (h *Handler) GetCachedCollections(c *gin.Context)
```

**功能**:
- 解析分页参数
- 调用缓存读取方法
- 实现分页逻辑
- 返回标准化的 API 响应

## 数据流程

```mermaid
graph TD
    A[客户端请求] --> B{缓存是否存在?}
    B -->|是| C[从Redis读取数据]
    B -->|否| D[返回404错误]
    C --> E[JSON反序列化]
    E --> F[应用分页]
    F --> G[返回响应]
    
    H[Token搜索请求] --> I{缓存是否可用?}
    I -->|是| J[在缓存中搜索]
    I -->|否| K[回退到API调用]
    J --> L{找到匹配?}
    L -->|是| M[排序并限制结果]
    L -->|否| K
    M --> N[返回匹配结果]
    K --> N
```

## 使用示例

### 1. 获取第一页缓存数据
```bash
curl "http://localhost:8080/api/cached-collections?page=1&page_size=50"
```

### 2. 搜索特定 token (在现有 API 中自动使用缓存)
```bash
curl "http://localhost:8080/api/recognized-ca/0x123.../1"
```

### 3. 手动触发缓存更新
```bash
curl -X POST "http://localhost:8080/api/trigger-collections-cache"
```

## 性能优势

### 缓存优势
- **响应速度**: 从 Redis 读取比 API 调用快 10-100 倍
- **减少外部依赖**: 降低对外部 API 的依赖
- **数据一致性**: 定期更新确保数据相对新鲜

### 智能回退
- **高可用性**: 缓存失败时自动使用 API
- **透明切换**: 用户无感知的服务降级
- **错误隔离**: 缓存问题不影响核心功能

## 监控和维护

### 日志监控
```bash
# 查看缓存读取日志
grep "cached collections" /var/log/app.log

# 查看回退到API的情况
grep "falling back to API" /var/log/app.log
```

### Redis 监控
```bash
# 检查缓存是否存在
redis-cli EXISTS v2_collections_cache

# 查看缓存大小
redis-cli MEMORY USAGE v2_collections_cache

# 查看缓存过期时间
redis-cli TTL v2_collections_cache
```

### 性能指标
- **缓存命中率**: 从缓存成功读取的请求比例
- **响应时间**: 缓存 vs API 调用的响应时间对比
- **错误率**: 缓存读取失败的频率

## 故障排除

### 常见问题

1. **缓存不存在**
   - 检查定时任务是否正常运行
   - 手动触发缓存更新
   - 检查 Redis 连接状态

2. **数据格式错误**
   - 检查缓存数据的 JSON 格式
   - 清除损坏的缓存数据
   - 重新触发缓存更新

3. **搜索结果不准确**
   - 检查搜索逻辑的匹配条件
   - 验证缓存数据的完整性
   - 对比 API 和缓存的数据差异

### 恢复步骤
1. 检查 Redis 服务状态
2. 验证缓存键是否存在
3. 手动触发缓存更新
4. 监控日志确认更新成功
5. 测试 API 端点响应

## 未来改进

1. **缓存预热**: 应用启动时自动加载缓存
2. **增量更新**: 支持部分数据更新而非全量替换
3. **多级缓存**: 添加内存缓存层提高性能
4. **缓存统计**: 添加详细的缓存使用统计
5. **自动清理**: 定期清理过期或无效的缓存数据
