# Distributed Locking Implementation

## Overview

This document describes the Redis-based distributed locking implementation for the real-time CA service. The distributed locking mechanism prevents race conditions during concurrent tweet processing across multiple service instances.

## Architecture

### Components

1. **RedisService**: Manages Redis connections and provides the foundation for distributed locking
2. **DistributedLockManager**: High-level interface for acquiring and managing distributed locks
3. **DistributedLock**: Represents an individual acquired lock with automatic refresh capabilities
4. **TwitterService Integration**: Uses distributed locking in `ProcessListTweet` method

### Key Features

- **Atomic Lock Operations**: Uses Redis SET with NX (not exists) and EX (expiration) options via Lua scripts
- **Automatic Lock Refresh**: Prevents lock expiration during long-running operations
- **Configurable Retry Strategy**: Exponential backoff with configurable parameters
- **Production-Ready Error Handling**: Comprehensive error handling and logging
- **Graceful Degradation**: Service continues to function even if Redis is unavailable
- **Graceful Shutdown**: Automatic cleanup of all distributed locks during service shutdown
- **Lock Tracking**: Tracks all active locks for monitoring and cleanup purposes

## Configuration

### Redis Configuration (service.toml)

```toml
[redis]
# Redis connection details
host = "localhost"
port = 6379
password = ""
db = 0

# Connection pool settings
pool_size = 10
min_idle_conns = 2
max_retries = 3

# Timeout settings (in seconds)
dial_timeout_sec = 5
read_timeout_sec = 3
write_timeout_sec = 3
pool_timeout_sec = 4
idle_timeout_sec = 300
idle_check_frequency_sec = 60

# Distributed locking configuration
lock_timeout_sec = 30          # Default lock TTL
lock_retry_delay_sec = 1       # Delay between lock acquisition retries
lock_max_retries = 10          # Maximum number of lock acquisition retries
lock_refresh_interval_sec = 10 # Interval for automatic lock refresh
lock_cleanup_timeout_sec = 30  # Timeout for cleanup operations during shutdown

# Key management configuration
key_prefix = "v2_"                    # Prefix for all Redis keys to avoid conflicts
cleanup_old_keys = true               # Whether to cleanup old keys on startup
old_key_patterns = ["tweet_lock:*"]   # Patterns of old keys to cleanup
```

### Environment-Specific Recommendations

#### Development
- `lock_timeout_sec = 10`
- `lock_max_retries = 5`
- `lock_retry_delay_sec = 1`

#### Production
- `lock_timeout_sec = 30`
- `lock_max_retries = 10`
- `lock_retry_delay_sec = 1`
- `lock_refresh_interval_sec = 10`

## Usage

### Basic Lock Acquisition

```go
// Create lock manager
lockManager := NewDistributedLockManager(redisService)

// Acquire lock with default options
lock, err := lockManager.AcquireLock(ctx, "my_lock_key", nil)
if err != nil {
    log.Error().Err(err).Msg("Failed to acquire lock")
    return
}

// Ensure lock is released
defer func() {
    if releaseErr := lock.ReleaseLock(); releaseErr != nil {
        log.Error().Err(releaseErr).Msg("Error releasing lock")
    }
}()

// Perform critical section work
// ...
```

### Custom Lock Options

```go
opts := &LockOptions{
    Timeout:    30 * time.Second,
    RetryDelay: 100 * time.Millisecond,
    MaxRetries: 5,
    Metadata:   "tweet_processing",
}

lock, err := lockManager.AcquireLock(ctx, lockKey, opts)
```

### Tweet Processing Integration

The `ProcessListTweet` method automatically uses distributed locking:

```go
func (s *TwitterService) ProcessListTweet(tweet Tweet, listType string) {
    // Distributed lock is automatically acquired using tweet.IdStr
    lockKey := CreateTweetLockKey(tweet.IdStr)

    // Lock is held during the entire tweet processing operation
    // Lock is automatically released when the function exits
}
```

## Lock Key Strategy

### Tweet Processing Locks
- **Pattern**: `tweet_lock:{tweet_id}`
- **Example**: `tweet_lock:1234567890123456789`
- **Purpose**: Prevents concurrent processing of the same tweet

### Lock Key Best Practices
1. Use descriptive prefixes (e.g., `tweet_lock:`, `user_lock:`)
2. Include unique identifiers (tweet ID, user ID, etc.)
3. Keep keys reasonably short but descriptive
4. Use consistent naming conventions

## Error Handling

### Lock Acquisition Failures
- **ErrNotObtained**: Lock is already held by another instance
- **Network Errors**: Redis connection issues
- **Timeout Errors**: Lock acquisition timeout exceeded

### Graceful Degradation
If Redis is unavailable:
1. Log warning about distributed locking being disabled
2. Continue processing without locking (single-instance mode)
3. Monitor Redis connectivity for automatic recovery

### Error Recovery
- Automatic retry with exponential backoff
- Lock expiration prevents deadlocks
- Connection pool handles Redis reconnection

## Monitoring and Metrics

### Key Metrics to Monitor
1. **Lock Acquisition Rate**: Successful vs failed lock acquisitions
2. **Lock Hold Time**: How long locks are held on average
3. **Lock Contention**: Frequency of lock acquisition failures
4. **Redis Connection Health**: Connection pool status

### Logging
- Lock acquisition attempts (DEBUG level)
- Successful lock acquisitions (INFO level)
- Lock acquisition failures (WARN level)
- Lock release operations (INFO level)
- Lock refresh operations (DEBUG level)

## Operational Considerations

### Deployment
1. Ensure Redis is deployed with high availability (Redis Sentinel or Cluster)
2. Configure appropriate Redis memory limits and eviction policies
3. Monitor Redis performance and connection limits
4. Set up Redis backup and recovery procedures

### Scaling
- Lock contention increases with the number of service instances
- Consider sharding strategies for high-throughput scenarios
- Monitor lock acquisition latency and adjust retry parameters

### Troubleshooting

#### High Lock Contention
- Increase `lock_retry_delay_sec` to reduce Redis load
- Decrease `lock_timeout_sec` if operations are quick
- Consider implementing lock-free algorithms for high-frequency operations

#### Lock Acquisition Timeouts
- Check Redis connectivity and performance
- Verify lock timeout settings are appropriate for operation duration
- Monitor for deadlock scenarios (should be prevented by TTL)

#### Memory Usage
- Monitor Redis memory usage for lock keys
- Ensure locks are properly released
- Set appropriate TTL values to prevent memory leaks

## Security Considerations

1. **Redis Authentication**: Use strong passwords for Redis instances
2. **Network Security**: Secure Redis network access with firewalls/VPNs
3. **Lock Metadata**: Avoid storing sensitive information in lock metadata
4. **Access Control**: Implement proper Redis ACLs in production

## Testing

### Unit Tests
- Lock acquisition and release
- Concurrent lock attempts
- Lock refresh mechanism
- Error handling scenarios

### Integration Tests
- Redis connectivity
- Multi-instance lock contention
- Network partition scenarios
- Redis failover testing

### Load Testing
- High-frequency lock operations
- Multiple concurrent instances
- Redis performance under load
- Lock acquisition latency measurement

## Graceful Shutdown and Lock Cleanup

The distributed locking system includes comprehensive cleanup mechanisms to ensure that locks are properly released when the service shuts down gracefully.

### Automatic Lock Tracking

The `DistributedLockManager` automatically tracks all active locks:

```go
// Get current active lock count
count := lockManager.GetActiveLockCount()

// Get all active lock keys
keys := lockManager.GetActiveLockKeys()
```

### Shutdown Manager Integration

The service uses a `ShutdownManager` that coordinates graceful shutdown:

1. **Signal Handling**: Listens for SIGINT and SIGTERM signals
2. **Lock Cleanup**: Releases all active distributed locks first
3. **Service Shutdown**: Stops background workers and HTTP server
4. **Timeout Handling**: Respects configured shutdown timeouts

### Cleanup Process

During shutdown, the system:

1. **Captures Active Locks**: Takes a snapshot of all currently held locks
2. **Concurrent Release**: Releases locks concurrently (limited to 10 concurrent operations)
3. **Timeout Handling**: Respects the `lock_cleanup_timeout_sec` configuration
4. **Error Handling**: Logs errors but continues cleanup for remaining locks
5. **Graceful Degradation**: Continues shutdown even if some locks fail to release

### Configuration

```toml
[redis]
# Timeout for cleanup operations during shutdown
lock_cleanup_timeout_sec = 30
```

### Example Usage

The cleanup is automatically handled by the service, but you can also trigger it manually:

```go
// Manual cleanup (typically not needed)
ctx := context.Background()
err := lockManager.CleanupAllLocks(ctx)
if err != nil {
    log.Error().Err(err).Msg("Error during lock cleanup")
}
```

### Best Practices

1. **Set Appropriate Timeouts**: Configure `lock_cleanup_timeout_sec` based on your expected number of locks
2. **Monitor Cleanup**: Watch logs during shutdown to ensure locks are being released
3. **Handle Failures Gracefully**: The system continues shutdown even if some locks fail to release
4. **Test Shutdown**: Regularly test graceful shutdown in your deployment pipeline

## Key Prefix Strategy and Version Management

The distributed locking system uses a key prefix strategy to avoid conflicts during deployments and version upgrades.

### Key Prefix Configuration

```toml
[redis]
# Prefix for all Redis keys to avoid conflicts
key_prefix = "v2_"

# Whether to cleanup old keys on startup
cleanup_old_keys = true

# Patterns of old keys to cleanup
old_key_patterns = ["tweet_lock:*", "old_cache:*"]
```

### How It Works

1. **Prefixed Keys**: All new locks use the configured prefix
   - Old format: `tweet_lock:123`
   - New format: `v2_tweet_lock:123`

2. **Startup Check**: Service checks for old keys on startup
   - Logs warnings about found old keys
   - Optionally cleans up old keys automatically

3. **Safe Deployment**: Multiple versions can coexist safely
   - v1 service uses `tweet_lock:*` keys
   - v2 service uses `v2_tweet_lock:*` keys
   - No conflicts between versions

### Deployment Scenarios

#### New Deployment (Recommended)
```toml
key_prefix = "v2_"
cleanup_old_keys = true
old_key_patterns = ["tweet_lock:*"]
```

#### Existing Deployment Upgrade
```toml
key_prefix = "v2_"
cleanup_old_keys = false  # Disable for safety
old_key_patterns = ["tweet_lock:*"]
```

#### Development Environment
```toml
key_prefix = "dev_"
cleanup_old_keys = true
old_key_patterns = ["tweet_lock:*", "v2_tweet_lock:*"]
```

### Migration Strategy

1. **Phase 1**: Deploy with cleanup disabled
   ```toml
   cleanup_old_keys = false
   ```

2. **Phase 2**: Monitor for old keys
   ```bash
   # Check logs for old key warnings
   grep "Found old keys" service.log
   ```

3. **Phase 3**: Enable cleanup after verification
   ```toml
   cleanup_old_keys = true
   ```

### Key Generation Functions

```go
// For backward compatibility
CreateTweetLockKey("123")                    // → "tweet_lock:123"

// With explicit prefix
CreateTweetLockKeyWithPrefix("v2_", "123")   // → "v2_tweet_lock:123"

// Using manager's configured prefix
lockManager.CreateTweetLockKey("123")        // → "v2_tweet_lock:123"
```
