# Collections Cache Scheduler

## 概述

本功能实现了一个定时任务，用于定期从 scattering.io API 获取 collections 数据并缓存到 Redis 中。

## 功能特性

### 定时任务配置
- **执行频率**: 每5天执行一次
- **执行时间**: 每5天的凌晨2点 (cron: `0 2 */5 * *`)
- **自动启动**: 应用启动时自动启动定时任务
- **优雅关闭**: 应用关闭时自动停止定时任务

### 数据获取逻辑
- **API 端点**: `https://scattering.io/api/collections`
- **分页获取**: 每页100条记录 (`page_size=100`)
- **自动遍历**: 自动遍历所有页面直到获取完所有数据
- **排序参数**: `sort_field=total_volume_in_24hours`, `sort_direction=desc`
- **请求间隔**: 页面请求之间有100ms延迟，避免对API造成压力

### 数据过滤条件
- **过滤规则**: 只缓存满足 `len(tags) > 0` 条件的 collection 数据
- **过滤日志**: 记录过滤前后的数据量统计

### 缓存实现
- **存储方式**: 使用 Redis 作为缓存存储
- **缓存键**: `v2_collections_cache` (使用版本化前缀)
- **过期时间**: 7天 (7 * 24 * 3600 秒)
- **数据格式**: JSON 序列化的 collection 数组

### 错误处理
- **网络错误**: API 请求失败时记录错误日志，不影响应用运行
- **Redis 错误**: 缓存操作失败时记录错误日志，不影响应用运行
- **数据解析错误**: JSON 解析失败时记录错误日志
- **API 错误**: API 返回错误状态码时记录详细错误信息

## 代码结构

### 核心组件

1. **Handler 结构体扩展**
   ```go
   type Handler struct {
       twitterService *services.TwitterService
       caService      *services.CAService
       tokenService   *services.TokenService
       redisService   *services.RedisService  // 新增
       scheduler      *cron.Cron              // 新增
   }
   ```

2. **数据结构定义**
   ```go
   type Collection struct {
       ChainId      int       `json:"chain_id"`
       Address      string    `json:"address"`
       Name         string    `json:"name"`
       Symbol       string    `json:"symbol"`
       Tags         []struct {
           Name  string `json:"name"`
           Color string `json:"color"`
           Rank  int    `json:"rank"`
           Type  int    `json:"type"`
       } `json:"tags"`
       TotalVolumeIn24Hours string `json:"total_volume_in_24hours"`
       // ... 其他字段
   }
   ```

3. **核心方法**
   - `StartScheduler()`: 启动定时任务调度器
   - `StopScheduler()`: 停止定时任务调度器
   - `collectionsScheduledTask()`: 定时任务执行函数
   - `fetchCollectionsData()`: 从API获取数据
   - `cacheCollectionsData()`: 缓存数据到Redis

## 使用方法

### 应用启动
定时任务会在应用启动时自动启动，无需手动配置。

### 日志监控
可以通过日志监控定时任务的执行情况：

```
INFO Starting scheduler...
INFO Cron scheduler started with collections cache update job
INFO Starting scheduled collections cache update task
INFO Filtered collections by tags total_collections=1500 filtered_collections=800
INFO Successfully cached collections data in Redis cached_collections=800
INFO Completed scheduled collections cache update task duration=45.2s
```

### 缓存数据访问
可以通过 Redis 客户端访问缓存的数据：

```bash
# 查看缓存键
redis-cli GET v2_collections_cache

# 查看缓存过期时间
redis-cli TTL v2_collections_cache
```

## 配置说明

### 依赖要求
- Redis 服务必须正常运行
- 网络连接能够访问 scattering.io API

### 性能考虑
- 每次执行可能需要几十秒到几分钟，取决于数据量
- 缓存数据大小取决于过滤后的 collection 数量
- Redis 内存使用量会增加（通常几MB到几十MB）

## 故障排除

### 常见问题

1. **定时任务未启动**
   - 检查应用启动日志是否有 "Starting scheduler..." 消息
   - 确认 Redis 服务正常运行

2. **API 请求失败**
   - 检查网络连接
   - 确认 scattering.io API 服务状态
   - 查看错误日志中的具体错误信息

3. **缓存失败**
   - 检查 Redis 连接状态
   - 确认 Redis 有足够的内存空间
   - 检查 Redis 配置和权限

### 日志级别
- **INFO**: 正常执行流程和统计信息
- **DEBUG**: 详细的分页请求信息
- **ERROR**: 错误情况和异常处理

## 测试

项目包含了基本的单元测试：

```bash
# 运行调度器生命周期测试
go test ./internal/api -v -run TestHandler_SchedulerLifecycle

# 运行过滤逻辑测试
go test ./internal/api -v -run TestCollectionsScheduledTask_FiltersByTags
```

## 未来改进

1. **配置化**: 将定时任务执行时间、缓存过期时间等参数配置化
2. **监控指标**: 添加 Prometheus 指标监控
3. **重试机制**: 添加 API 请求失败的重试逻辑
4. **增量更新**: 支持增量数据更新而非全量替换
5. **多实例协调**: 在多实例部署时避免重复执行
